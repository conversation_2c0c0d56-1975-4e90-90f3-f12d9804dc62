# TODO List
## TASKS
- [x] Test deployment #6

### Layout
- [x] The References button should be moved up between the Dashboard and Share buttons because the references can be used in any document. The user should be able to manage references at any time even on the Dashboard.
- [x] The Outline and Writing panel buttons are currently on their own row, but should be on the same row as the Document Title, Metadata, History, and Export buttons.
- [x] The Dashboard should be renamed to Documents.
- [x] The Documents shown on the dashboard should also show the created date.
- [x] Themes should be simplified to Light and Dark. The custom theme options should be removed.

### Document Sharing
- [x] Core Backend & Share Modal UI [^sharing_core_done]
- [x] Dashboard UI Integration [^dashboard_ui_details]
    - [x] Data Fetching for owned and shared documents
    - [x] UI Update for 'My Documents' and 'Shared With Me' sections
    - [x] Display Shared Info (title, owner, permissions)
    - [x] Implement 'Remove' / 'Leave Share' button
- [ ] Shared documents should have a different background color to distinguish it from an owned document.
- [ ] Shared documents should not show the guest the Metadata, History, or Export buttons.
- [ ] Shared documents should not allow the guest to add citations.
- [ ] Shared documents that are read only should not have editable fields.

### Document Chat
- [x] DB Schema Definition & Migration [^chat_db_schema]
- [x] WebSocket Event & Payload Definition [^chat_ws_events]
- [x] Server-Side Logic (add/get messages, broadcast) [^chat_server_logic]
- [x] Client-Side Logic in `useDocument()` hook (state, send, receive) [^chat_client_hook]
- [x] UI Component (`DocumentChatPanel.tsx`)
    - [x] Create component, display messages, input field, send button
    - [x] Refined message styling (avatars, grouping, alignment, bubble expansion, etc.) [^chat_ui_styling_done]
    - [x] Integrate `DocumentChatPanel` into `DocumentPage.tsx`

### Future Feature Development Roadmap
- [x] User Presence (UI Integration) [^user_presence_ui_details]
    - [x] Backend and core client state implemented
    - [x] UI components to display present users & toast notifications (Avatars, Tooltips)
    - [x] Toast notifications for join/leave events (verified existing functionality)
- [x] Secure Shareable Links (Phase 1 - Tokenized Links) [^secure_share_links_phase1]
    - [x] Generate unique, permissioned tokens for document sharing via links.
    - [x] API endpoint to create these tokenized links (owner only).
    - [x] Frontend UI in Share Modal to generate these links (deprecate direct `?mode=` links).
    - [x] Backend logic to validate tokens and grant access by adding to `document_shares`.
- [x] Document Access Request System (Phase 2 - Advanced) [^access_request_system]
    - [x] UI for users to request access to a document if they land on a base link without permissions.
    - [x] Notification system for document owners about access requests.
    - [x] UI for owners to approve/deny requests and set permissions.
- [x] Inline Commenting (Advanced) [^inline_commenting_details]
- [x] Client-Side User ID in Save Deltas (Verification) [^client_id_deltas_verify]
- [x] Robust Outline Updates & Moves [^outline_moves_details]
- [x] Text Diffing for Writing Panel Content [^text_diffing_details]
- [ ] Integrated AI Suggestions with differential accept/reject previews.
- [ ] Teaching Administration dashboard showing analytics on document usage and user behavior of student accounts.
- [ ] Change of document ownership. Add new button to Documents Dashboard to transfer ownership to another user.
- [ ] New features announced in the app with a notification badge on the app header with a modal to view the announcement. The source of the announcements should be retrieved from a private github repo that only the core team has access to.
- [ ] Popout media player for audio and video notes.

## BUGS
- [x] A user invited to a document via the share modal invite link to edit is not taken to the editable version of the document.
- [x] The document panel separator between the Outline and Writing panels does not always drag the direction the user drags, but rather the opposite.
- [x] A user visiting a document link that has not yet been granted access gets a blank document and a not authorized toast message instead of being shown a special message to request access to the document from the owner.
- [x] Dragging items in the outline panel is finniky and it is hard to target the intended dropzone. Often items being dragged will dissapear and be lost.
- [x] If someone clicks the share link and are not logged in, they should be taken to the auth page instead of seeing an error.
- [x] The Sharing modal is having trouble loading the users the document is being shared with.
- [x] Outline drag and drop is not working correctly. [^outline_dnd_fixed]
    - [x] Each item should itself be a dropzone and the item dropped onto it should be added as a child of that item.
    - [x] Each item should have a drag handle to initiate dragging.
    - [x] Each item should have a before and after dropzone to change sibling order or move a child to a different parent or become a sibling.
- [x] Minimap is not displaying content or functioning properly. [^minimap_fixed]
- [ ] Guest users in a shared document with edit permissions are not able to make changes to the document. Check websocket changesets?

---
[^sharing_core_done]: Core backend for document sharing (direct user invites) is implemented, including the `document_shares` table, APIs (`GET/POST /api/documents/:documentId/shares`, `PUT/DELETE /api/documents/:documentId/shares/:targetUserId`, `GET /api/users/search`) for managing shares, and the "Share Modal" (`client/src/components/share-modal.tsx`) which uses these APIs. The main documents list API (`GET /api/documents`) returns `{ owned: OwnedDocument[], shared: SharedDocumentInfo[] }`. Access to documents by shared users via `GET /api/documents/:id` and `GET /api/documents/:id/content` is also implemented.

[^secure_share_links_phase1]: Implement a system for generating secure, tokenized shareable links. This involves creating unique tokens associated with a document and a specific permission level ('view' or 'edit'). The Share Modal should allow owners to generate these links. Accessing such a link should validate the token and grant the user the specified permission by adding an entry to `document_shares`. This system aims to replace insecure query parameter-based link sharing (e.g., `?mode=readonly`). This phase focuses on the token generation, validation, and access granting. The `SharedDocumentPage.tsx` will need to be adapted to handle these new link types and correctly set read-only state based on backend-confirmed permissions.

[^access_request_system]: (Advanced Feature) Develop a system where users attempting to access a document via a base link (e.g., `/shared/${documentId}`) without existing permissions can request access. This system would notify the document owner, who can then approve or deny the request and set appropriate permissions. This involves new UI for requesting and managing access, a notification mechanism, and backend logic to handle these requests and approvals.

[^dashboard_ui_details]: Task involves focusing on `client/src/pages/dashboard.tsx` to: 1. Ensure data fetching from `GET /api/documents` correctly processes `{ owned: [], shared: [] }`. 2. Update UI to display 'My Documents' (owned) and 'Shared With Me' sections. 3. For shared documents, display title, owner's username, and permission level. 4. Implement 'Remove'/'Leave Share' button using `DELETE /api/documents/:documentId/shares/:targetUserId`.

[^chat_db_schema]: Define and migrate a `document_chat_messages` table with columns: `id`, `documentId` (FK), `userId` (FK), `username` (denormalized), `messageText`, `createdAt`. To be located in `shared/schema.ts`.

[^chat_ws_events]: Define `CHAT_MESSAGE_SENT` (client->server) and `NEW_CHAT_MESSAGE` (server->client) WebSocket event types and their payloads (message content, sender info, timestamp) in `shared/schema.ts`. This might extend existing structures like `DocumentChangeType`.

[^chat_server_logic]: Implement in `server/index.ts` and `server/storage.ts`: 1. Storage method `addChatMessage(documentId, userId, username, messageText)`. 2. Handle incoming `CHAT_MESSAGE_SENT` in WebSocket: save message, then broadcast `NEW_CHAT_MESSAGE` to other users in the room. 3. API endpoint `GET /api/documents/:documentId/chat` using a new storage method `getChatMessages(documentId)` for historical messages.

[^chat_client_hook]: Implement in `client/src/hooks/use-document.ts`: 1. Add state for `chatMessages: ChatMessage[]`. 2. Expose `sendChatMessage(messageText: string)` function to send `CHAT_MESSAGE_SENT` WebSocket message. 3. Process incoming `NEW_CHAT_MESSAGE` events in WebSocket `onmessage` handler to update `chatMessages` state. 4. Fetch historical messages on document load using the API and populate state.

[^chat_ui_styling_done]: The message presentation within `DocumentChatPanel.tsx` has been completed. This includes: sender's avatar positioned correctly (left for current user, right for others); consecutive messages from the same sender grouped into a single bubble; message bubbles expanding to fill available width; text within all message bubbles left-aligned; and content within any user's bubble starting from the left edge of the bubble.

[^user_presence_ui_details]: Backend and core client state (`presentUsers` in `useDocument`) are implemented. UI integration is pending. This involves updating or creating UI components (e.g., `<UserPresence />`, `<CollaborationStatus />`) in `DocumentPage.tsx` and `SharedDocumentPage.tsx` to display the list of users currently viewing the document. Consider displaying user avatars/initials. Implement toast notifications for users joining/leaving (excluding self).

[^inline_commenting_details]: Advanced task. Involves: 1. Designing robust text anchoring mechanism. 2. Defining DB schema for comments/anchors. 3. Implementing real-time WebSocket updates for comments. 4. Developing UI/UX for selecting text, creating/displaying/resolving comments similar to Google Docs.

[^client_id_deltas_verify]: Client now fetches authenticated user ID and uses it in `documentChange` messages. Verify and ensure this ID is consistently sent in ALL relevant WebSocket message payloads (e.g., `CHAT_MESSAGE_SENT`). Remove any remaining random client-side user IDs for payloads. Status: Mostly resolved, needs final check.

[^outline_moves_details]: Enhance client-side delta generation (`diffOutlineItems`) and server-side application (`applyDocumentChanges`) for outline items. Improve handling of additions/removals in nested structures. Implement `OUTLINE_ITEM_MOVED` change type for reordering/moving items.

[^text_diffing_details]: Optimize `WRITING_SECTION_MODIFIED` deltas. Currently sends full content. Integrate a text-diffing library (e.g., `diff-match-patch`) on client to generate patches. Modify server to apply patches, reducing payload size.

[^outline_dnd_fixed]: Complete overhaul of outline drag-and-drop functionality. Implemented: 1. Drag handles (⋮⋮) for initiating drag operations. 2. Visual drop zones with blue theme-consistent colors for before/after positioning. 3. Child drop zones allowing items to be dropped onto other items to create parent-child relationships. 4. Precise event handling preventing event bubbling issues in nested structures. 5. Enhanced UX with "+" buttons in drop zones for quick sibling addition. 6. Proper state management and auto-save functionality. 7. Support for deeply nested outline structures with correct numbering updates.

[^minimap_fixed]: Fixed minimap functionality by correcting contentRef targeting. The minimap was receiving a reference to the wrong DOM container, preventing it from finding outline items. Solution involved creating a dedicated `mainContentRef` pointing directly to the scrollable content area containing `.outline-list-container` and `.outline-item` elements. Also optimized container sizing from 192px to 96px to reduce wasted space while maintaining 80px canvas width.
