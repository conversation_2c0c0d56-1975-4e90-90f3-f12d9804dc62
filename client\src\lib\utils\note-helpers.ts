import { Note } from '../types';

/**
 * Infers the note type based on note content and attributes
 */
export function inferNoteType(note: Note): 'text' | 'image' | 'video' | 'file' {
  // If type is explicitly set, use that
  if (note.type) return note.type as 'text' | 'image' | 'video' | 'file';
  
  // Otherwise infer based on content
  const imageUrls = note.imageUrls || [];
  const videoUrls = note.videoUrls || [];
  const fileUrls = note.fileUrls || [];
  
  if (note.primaryAssetUrl) {
    if (note.primaryAssetUrl.match(/\.(jpg|jpeg|png|gif|bmp|svg|webp)$/i)) {
      return 'image';
    } else if (note.primaryAssetUrl.match(/\.(mp4|webm|mov|avi|wmv|mkv)$/i)) {
      return 'video';
    } else {
      return 'file';
    }
  }
  
  if (imageUrls.length > 0) {
    return 'image';
  }
  
  if (videoUrls.length > 0) {
    return 'video';
  }
  
  if (fileUrls.length > 0) {
    return 'file';
  }
  
  return 'text';
}

/**
 * Returns the icon class for a specific note type
 */
export function getNoteTypeIcon(note: Note): string {
  const noteType = note.type || inferNoteType(note);
  
  switch (noteType) {
    case 'text':
      return 'ri-file-text-line';
    case 'image':
      return 'ri-image-line';
    case 'video':
      return 'ri-video-line';
    case 'file':
      return 'ri-file-line';
    default:
      return 'ri-file-text-line';
  }
}

/**
 * Returns the appropriate color classes for a specific note type
 */
export function getNoteTypeColors(note: Note): { text: string; border: string; bg: string } {
  const noteType = note.type || inferNoteType(note);
  
  switch (noteType) {
    case 'text':
      return {
        text: 'text-icon-blue',
        border: 'border-primary/20',
        bg: 'bg-primary/5'
      };
    case 'image':
      return {
        text: 'text-icon-green',
        border: 'border-[var(--icon-green)]/20',
        bg: 'bg-[var(--icon-green)]/5'
      };
    case 'video':
      return {
        text: 'text-icon-purple',
        border: 'border-[var(--icon-purple)]/20',
        bg: 'bg-[var(--icon-purple)]/5'
      };
    case 'file':
      return {
        text: 'text-icon-amber',
        border: 'border-[var(--icon-amber)]/20',
        bg: 'bg-[var(--icon-amber)]/5'
      };
    default:
      return {
        text: 'text-muted-foreground',
        border: 'border-border',
        bg: 'bg-accent'
      };
  }
}

/**
 * Gets the word count from a markdown string
 */
export function getWordCount(text: string): number {
  if (!text) return 0;
  
  // Remove markdown formatting and special characters
  const plainText = text
    .replace(/```[\s\S]*?```/g, '') // Code blocks
    .replace(/`.*?`/g, '') // Inline code
    .replace(/\[.*?\]\(.*?\)/g, '') // Links
    .replace(/[#*_~`>:"'|]/g, '') // Special markdown characters
    .replace(/\n/g, ' ') // Newlines to spaces
    .trim();
    
  // Split by whitespace and filter out empty strings
  const words = plainText.split(/\s+/).filter(Boolean);
  
  return words.length;
}

/**
 * Gets linked outline items from a note
 */
export function getLinkedOutlineItems(note: Note, outlineItems: any[] = []): any[] {
  if (!note.linkedOutlineId || !outlineItems.length) {
    return [];
  }

  return outlineItems.filter(item => item.id === note.linkedOutlineId);
}