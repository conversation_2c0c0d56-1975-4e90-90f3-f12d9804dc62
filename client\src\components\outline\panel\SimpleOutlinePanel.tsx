import React, { useRef, useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import NewMinimap from '../NewMinimap'; // Updated import for NewMinimap
import { OutlineItem } from './item';
import { OutlineItem as OutlineItemType, Note } from '@/lib/types';
import { calculateOutlineNumbers } from '@/lib/utils/outline';
import { ConfirmModal } from '@/components/ui/confirm-modal';
import { nanoid } from 'nanoid';
import { Input } from "@/components/ui/input";
import { useToast } from '@/hooks/use-toast';

interface SimpleOutlinePanelProps {
  outline: OutlineItemType[];
  onOutlineChange: (outline: OutlineItemType[]) => void;
  onAddItem: (parentId?: string) => void;
  onImportOutline: () => void;
  // Note-related props
  notes: Note[];
  onNoteCreate: (linkedOutlineId: string, options?: { type?: 'text' | 'image' | 'video' | 'file' }) => Note;
  onNoteUpdate: (updatedNote: Note, forceSaveNow?: boolean) => void;
  onNoteDelete: (noteId: string) => void;
  onNoteDuplicate: (noteId: string) => void;
  onFileUpload: (file: File) => Promise<string>;
  onLinkNoteToOutlineItem: (noteId: string, outlineItemId: string) => void;
  onDeleteOutlineItemWithNotes: (outlineItemId: string) => void;
  popOutPanel: () => void;
  isPoppedOut?: boolean;
  activeOutlineItemId?: string | null;
}

export function SimpleOutlinePanel({
  outline,
  onOutlineChange,
  onAddItem,
  onImportOutline,
  notes,
  onNoteCreate,
  onNoteUpdate,
  onNoteDelete,
  onNoteDuplicate,
  onFileUpload,
  onLinkNoteToOutlineItem,
  onDeleteOutlineItemWithNotes,
  popOutPanel,
  isPoppedOut,
  activeOutlineItemId,
}: SimpleOutlinePanelProps) {
  const contentRef = useRef<HTMLDivElement>(null);
  const mainContentRef = useRef<HTMLDivElement>(null);
  const [outlineSearchTerm, setOutlineSearchTerm] = useState("");

  // Updated SearchResult structure
  interface SearchResult {
    item: OutlineItemType;
    matchDetails: {
      type: 'outlineTitle' | 'noteTitle' | 'noteContent';
      noteId?: string;
    };
  }
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [currentSearchResultIndex, setCurrentSearchResultIndex] = useState(-1);

  // Simple drag and drop state
  const [draggedItem, setDraggedItem] = useState<OutlineItemType | null>(null);
  const [dragOverTarget, setDragOverTarget] = useState<{id: string, position: 'before' | 'after' | 'child'} | null>(null);
  const [hoveredItemPath, setHoveredItemPath] = useState<string[]>([]);

  // Note drag and drop state
  const [draggedNote, setDraggedNote] = useState<Note | null>(null);
  const [noteDragOverTarget, setNoteDragOverTarget] = useState<{
    type: 'note' | 'outline-item';
    id: string;
    position?: 'before' | 'after';
  } | null>(null);
  const [isConfirmDeleteDialogOpen, setIsConfirmDeleteDialogOpen] = useState(false);
  const [outlineItemToDeleteId, setOutlineItemToDeleteId] = useState<string | null>(null);
  const { toast } = useToast();

  // Function to get flat list of search results
  const getFlatSearchResults = (items: OutlineItemType[], searchTerm: string, allNotes: Note[]): SearchResult[] => {
    let results: SearchResult[] = [];
    if (!searchTerm) return results;
    const lowerSearchTerm = searchTerm.toLowerCase();

    const recurse = (currentItems: OutlineItemType[]) => {
      for (const item of currentItems) {
        // Check outline item title
        if (item.title.toLowerCase().includes(lowerSearchTerm)) {
          results.push({
            item,
            matchDetails: { type: 'outlineTitle' }
          });
        }

        // Check linked notes for this outline item
        const linkedNotes = allNotes.filter(note => note.linkedOutlineId === item.id);
        for (const note of linkedNotes) {
          let noteAdded = false;
          if (note.title.toLowerCase().includes(lowerSearchTerm)) {
            results.push({
              item,
              matchDetails: { type: 'noteTitle', noteId: note.id }
            });
            noteAdded = true;
          }
          if (!noteAdded && note.content && note.content.toLowerCase().includes(lowerSearchTerm)) {
            results.push({
              item,
              matchDetails: { type: 'noteContent', noteId: note.id }
            });
          }
        }

        // Recurse into children
        if (item.children && item.children.length > 0) {
          recurse(item.children);
        }
      }
    };

    recurse(items);
    return results;
  };

  // Helper function to find an item by ID
  const findItemById = (items: OutlineItemType[], id: string): OutlineItemType | null => {
    for (const item of items) {
      if (item.id === id) return item;
      if (item.children) {
        const found = findItemById(item.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  // Helper function to find path to item
  const findPathToItem = (items: OutlineItemType[], targetId: string, currentPath: string[] = []): string[] => {
    for (const item of items) {
      const newPath = [...currentPath, item.id];
      if (item.id === targetId) {
        return newPath;
      }
      if (item.children) {
        const found = findPathToItem(item.children, targetId, newPath);
        if (found.length > 0) return found;
      }
    }
    return [];
  };

  // Search effect
  useEffect(() => {
    if (outlineSearchTerm.trim()) {
      const results = getFlatSearchResults(outline, outlineSearchTerm.trim(), notes);
      setSearchResults(results);
      setCurrentSearchResultIndex(results.length > 0 ? 0 : -1);
    } else {
      setSearchResults([]);
      setCurrentSearchResultIndex(-1);
    }
  }, [outlineSearchTerm, outline, notes]);

  // Determine which outline to display
  const filteredOutline = useMemo(() => {
    if (!outlineSearchTerm.trim() || searchResults.length === 0) {
      return outline;
    }

    // Create a set of item IDs that should be shown
    const itemsToShow = new Set<string>();
    const pathsToShow = new Set<string>();

    searchResults.forEach(result => {
      const path = findPathToItem(outline, result.item.id);
      path.forEach(id => {
        itemsToShow.add(id);
        pathsToShow.add(id);
      });
    });

    // Filter the outline to only show relevant items
    const filterItems = (items: OutlineItemType[]): OutlineItemType[] => {
      return items.filter(item => itemsToShow.has(item.id)).map(item => ({
        ...item,
        children: item.children ? filterItems(item.children) : []
      }));
    };

    return filterItems(outline);
  }, [outline, outlineSearchTerm, searchResults]);

  // Helper function to remove an item from the outline
  const removeItem = (items: OutlineItemType[], id: string): { items: OutlineItemType[], removed: OutlineItemType | null } => {
    let removed: OutlineItemType | null = null;

    const newItems = items.filter(item => {
      if (item.id === id) {
        removed = item;
        return false;
      }
      return true;
    }).map(item => {
      if (item.children) {
        const result = removeItem(item.children, id);
        if (result.removed) {
          removed = result.removed;
        }
        return { ...item, children: result.items };
      }
      return item;
    });

    return { items: newItems, removed };
  };

  // Helper function to add an item at a specific position
  const addItemAtPosition = (
    items: OutlineItemType[], 
    targetId: string, 
    newItem: OutlineItemType, 
    position: 'before' | 'after' | 'child'
  ): OutlineItemType[] => {
    return items.map((item, index) => {
      if (item.id === targetId) {
        if (position === 'child') {
          return {
            ...item,
            children: [...(item.children || []), newItem]
          };
        } else if (position === 'before') {
          // Insert before this item - handled at parent level
          return item;
        } else if (position === 'after') {
          // Insert after this item - handled at parent level
          return item;
        }
      }
      
      if (item.children) {
        const updatedChildren = addItemAtPosition(item.children, targetId, newItem, position);
        if (updatedChildren !== item.children) {
          return { ...item, children: updatedChildren };
        }
      }
      
      return item;
    }).flatMap((item, index) => {
      if (item.id === targetId) {
        if (position === 'before') {
          return [newItem, item];
        } else if (position === 'after') {
          return [item, newItem];
        }
      }
      return [item];
    });
  };

  // Drag and drop handlers
  const handleDragStart = (item: OutlineItemType) => {
    setDraggedItem(item);
  };

  const handleDragOver = (targetId: string, position: 'before' | 'after' | 'child') => {
    if (targetId === '') {
      setDragOverTarget(null);
      return;
    }

    // Don't allow dropping on self or descendants
    if (draggedItem && (targetId === draggedItem.id || isDescendant(draggedItem, targetId))) {
      setDragOverTarget(null);
      return;
    }

    setDragOverTarget({ id: targetId, position });
  };

  const handleDrop = (draggedId: string, targetId: string, position: 'before' | 'after' | 'child') => {
    if (!draggedItem || draggedId !== draggedItem.id) {
      return;
    }

    // Don't allow dropping on self or descendants
    if (targetId === draggedItem.id || isDescendant(draggedItem, targetId)) {
      toast({
        title: "Invalid drop",
        description: "Cannot drop an item on itself or its descendants",
        variant: "destructive"
      });
      return;
    }



    // Remove the dragged item from its current position
    const { items: outlineWithoutDragged, removed } = removeItem(outline, draggedId);

    if (!removed) {
      console.error('❌ Could not find dragged item to remove');
      return;
    }

    // Add the item at the new position
    const newOutline = addItemAtPosition(outlineWithoutDragged, targetId, removed, position);

    onOutlineChange(newOutline);

    toast({
      title: "Item moved",
      description: `"${removed.title}" moved successfully`,
    });
  };

  const handleDragEnd = () => {
    setDraggedItem(null);
    setDragOverTarget(null);
  };

  // Note drag and drop handlers
  const handleNoteDragStart = (note: Note) => {
    setDraggedNote(note);
  };

  const handleNoteDragOver = (targetType: 'note' | 'outline-item', targetId: string, position?: 'before' | 'after') => {
    if (targetId === '') {
      setNoteDragOverTarget(null);
      return;
    }

    // Don't allow dropping on self
    if (draggedNote && targetType === 'note' && targetId === draggedNote.id) {
      setNoteDragOverTarget(null);
      return;
    }

    setNoteDragOverTarget({ type: targetType, id: targetId, position });
  };

  const handleNoteDrop = (draggedNoteId: string, targetType: 'note' | 'outline-item', targetId: string, position?: 'before' | 'after') => {
    if (!draggedNote || draggedNoteId !== draggedNote.id) {
      return;
    }

    const updatedNotes = [...notes];
    const draggedNoteIndex = updatedNotes.findIndex(n => n.id === draggedNoteId);

    if (draggedNoteIndex === -1) {
      return;
    }

    const noteToMove = updatedNotes[draggedNoteIndex];

    if (targetType === 'outline-item') {
      // Moving note to a different outline item
      const targetOutlineNotes = updatedNotes.filter(n => n.linkedOutlineId === targetId);
      const newPosition = targetOutlineNotes.length;

      updatedNotes[draggedNoteIndex] = {
        ...noteToMove,
        linkedOutlineId: targetId,
        position: newPosition,
        updatedAt: new Date().toISOString()
      };
    } else if (targetType === 'note') {
      // Reordering notes within the same or different outline items
      const targetNote = updatedNotes.find(n => n.id === targetId);
      if (!targetNote) return;

      const targetOutlineId = targetNote.linkedOutlineId;
      const targetOutlineNotes = updatedNotes
        .filter(n => n.linkedOutlineId === targetOutlineId)
        .sort((a, b) => (a.position || 0) - (b.position || 0));

      const targetNoteIndex = targetOutlineNotes.findIndex(n => n.id === targetId);
      let newPosition: number;

      if (position === 'before') {
        newPosition = targetNote.position || targetNoteIndex;
      } else {
        newPosition = (targetNote.position || targetNoteIndex) + 1;
      }

      // Update the dragged note
      updatedNotes[draggedNoteIndex] = {
        ...noteToMove,
        linkedOutlineId: targetOutlineId,
        position: newPosition,
        updatedAt: new Date().toISOString()
      };

      // Reorder other notes in the target outline item
      updatedNotes.forEach((note, index) => {
        if (note.linkedOutlineId === targetOutlineId && note.id !== draggedNoteId) {
          const currentPos = note.position || 0;
          if (currentPos >= newPosition) {
            updatedNotes[index] = {
              ...note,
              position: currentPos + 1,
              updatedAt: new Date().toISOString()
            };
          }
        }
      });

      // Ensure all notes in the target outline have positions (backward compatibility)
      const targetNotes = updatedNotes.filter(n => n.linkedOutlineId === targetOutlineId);
      targetNotes.forEach((note, index) => {
        const noteIndex = updatedNotes.findIndex(n => n.id === note.id);
        if (noteIndex !== -1 && updatedNotes[noteIndex].position === undefined) {
          updatedNotes[noteIndex] = {
            ...updatedNotes[noteIndex],
            position: index,
            updatedAt: new Date().toISOString()
          };
        }
      });
    }

    // Save the updated notes
    console.log('Saving updated notes after drag:', updatedNotes.map(n => ({ id: n.id, title: n.title, linkedOutlineId: n.linkedOutlineId })));
    updatedNotes.forEach(note => {
      console.log('Calling onNoteUpdate for note:', note.id, 'linkedTo:', note.linkedOutlineId);
      onNoteUpdate(note, true); // Force save immediately
    });

    // Clear drag state immediately after successful drop
    setDraggedNote(null);
    setNoteDragOverTarget(null);

    toast({
      title: "Note moved",
      description: `"${noteToMove.title}" moved successfully`,
    });
  };

  const handleNoteDragEnd = () => {
    setDraggedNote(null);
    setNoteDragOverTarget(null);
  };

  // Add sibling functionality
  const handleAddSibling = (targetId: string, position: 'before' | 'after') => {
    const newItem: OutlineItemType = {
      id: nanoid(),
      title: "New Section",
      number: "",
      children: [],
      linkedNotes: []
    };

    const addSiblingToOutline = (items: OutlineItemType[]): OutlineItemType[] => {
      const result: OutlineItemType[] = [];

      for (let i = 0; i < items.length; i++) {
        const item = items[i];

        if (item.id === targetId) {
          if (position === 'before') {
            result.push(newItem, item);
          } else {
            result.push(item, newItem);
          }
        } else {
          // Check if target is in children
          if (item.children && item.children.length > 0) {
            const updatedChildren = addSiblingToOutline(item.children);
            if (updatedChildren !== item.children) {
              result.push({ ...item, children: updatedChildren });
            } else {
              result.push(item);
            }
          } else {
            result.push(item);
          }
        }
      }

      return result;
    };

    const newOutline = addSiblingToOutline(outline);
    const numberedOutline = calculateOutlineNumbers(newOutline);
    onOutlineChange(numberedOutline);
    notifyMinimapChange();
  };

  // Helper function to check if an item is a descendant of another
  const isDescendant = (ancestor: OutlineItemType, descendantId: string): boolean => {
    if (!ancestor.children) return false;
    
    for (const child of ancestor.children) {
      if (child.id === descendantId) return true;
      if (isDescendant(child, descendantId)) return true;
    }
    
    return false;
  };

  const handleUpdateItem = (updatedItem: OutlineItemType) => {
    const updateInOutline = (items: OutlineItemType[]): OutlineItemType[] => {
      return items.map(item => {
        if (item.id === updatedItem.id) {
          return updatedItem;
        }
        if (item.children) {
          return { ...item, children: updateInOutline(item.children) };
        }
        return item;
      });
    };
    
    onOutlineChange(updateInOutline(outline));
  };

  const getFlatOutlineItems = (items: OutlineItemType[]): OutlineItemType[] => {
    const flat: OutlineItemType[] = [];
    const traverse = (items: OutlineItemType[]) => {
      items.forEach(item => {
        flat.push(item);
        if (item.children) {
          traverse(item.children);
        }
      });
    };
    traverse(items);
    return flat;
  };

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (outlineItemToDeleteId) {
      onDeleteOutlineItemWithNotes(outlineItemToDeleteId);
      setOutlineItemToDeleteId(null);
    }
    setIsConfirmDeleteDialogOpen(false);
  };

  const handleDeleteCancel = () => {
    setOutlineItemToDeleteId(null);
    setIsConfirmDeleteDialogOpen(false);
  };

  // Notify minimap of changes
  const notifyMinimapChange = () => {
    if (mainContentRef.current) {
      const event = new CustomEvent('outlineChanged');
      mainContentRef.current.dispatchEvent(event);
    }
  };

  return (
    <div className="panel relative flex-1 flex flex-col overflow-hidden">
      {/* Fixed header - styled like notes panel */}
      <div className="bg-background flex justify-between items-center px-3 py-2 border-b border-border z-10">
        <h2 className="font-medium text-sm sm:text-base text-foreground flex items-center">
          <i className="ri-list-check mr-1.5 text-muted-foreground"></i>
          <span>Outline</span>
        </h2>
        <div className="flex gap-1">
          <Button
            variant="outline"
            size="sm"
            className="h-7 text-xs border-border px-2"
            title="Add new outline section"
            onClick={() => {
              onAddItem();
              // Notify minimap that outline will change
              if (mainContentRef.current) {
                setTimeout(() => {
                  const event = new CustomEvent('outlineChanged');
                  mainContentRef.current?.dispatchEvent(event);
                }, 100); // Short delay to allow DOM to update
              }
            }}
          >
            <i className="ri-add-line text-sm"></i>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-7 text-xs px-2"
            title="Import outline from document"
            onClick={onImportOutline}
          >
            <i className="ri-download-2-line text-sm"></i>
          </Button>
          {isPoppedOut ? (
            <Button
              variant="ghost"
              size="sm"
              className="h-7 text-xs px-2"
              title="Pop in panel"
              onClick={() => window.close()}
            >
              <i className="ri-login-box-line text-sm"></i>
            </Button>
          ) : (
            <Button
              variant="ghost"
              size="sm"
              className="h-7 text-xs px-2"
              title="Pop out panel"
              onClick={popOutPanel}
            >
              <i className="ri-external-link-line text-sm"></i>
            </Button>
          )}
        </div>
      </div>

      {/* Search Input */}
      <div className="p-2 border-b border-border">
        <div className="flex items-center gap-2">
          <div className="relative flex-grow">
            <i className="ri-search-line absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground text-xs z-10"></i>
            <Input
              type="text"
              placeholder="Search Outline & Notes"
              className="w-full rounded-md border-border pl-6 pr-10 text-xs h-7 bg-background text-foreground"
              value={outlineSearchTerm}
              onChange={(e) => {
                setOutlineSearchTerm(e.target.value);
              }}
            />
            {outlineSearchTerm && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 text-muted-foreground hover:text-foreground"
                onClick={() => {
                  setOutlineSearchTerm("");
                  setSearchResults([]);
                  setCurrentSearchResultIndex(-1);
                }}
                title="Clear search"
              >
                <i className="ri-close-line text-sm"></i>
              </Button>
            )}
          </div>
          <Button
            variant="outline"
            size="icon"
            className="h-7 w-7 border-border"
            title="Search navigation"
            disabled={searchResults.length === 0}
          >
            <i className="ri-navigation-line text-xs"></i>
          </Button>
        </div>

        {/* Search results info */}
        {outlineSearchTerm && (
          <div className="mt-1 text-xs text-muted-foreground">
            {searchResults.length > 0 ? (
              <>
                {searchResults.length} result{searchResults.length !== 1 ? 's' : ''} found
                {currentSearchResultIndex >= 0 && (
                  <span> • Showing result {currentSearchResultIndex + 1}</span>
                )}
              </>
            ) : (
              'No results found'
            )}
          </div>
        )}
      </div>

      {/* Content area with minimap */}
      <div ref={contentRef} className="flex-1 flex overflow-hidden">
        {/* Main content */}
        <div ref={mainContentRef} className="flex-1 overflow-y-auto">
          <div className="relative min-h-[40px] outline-list-container overflow-visible">
            {filteredOutline.length > 0 ? (
              <div className="space-y-0.5">
                {filteredOutline.map((item, index) => (
                  <OutlineItem
                    key={item.id}
                    item={item}
                    isGhost={false}
                    index={index}
                    activeId={null}
                    depth={0}
                    hoveredItemPath={hoveredItemPath}
                    onHover={(itemId) => {
                      const path = findPathToItem(filteredOutline, itemId);
                      setHoveredItemPath(path);
                    }}
                    onUpdate={(updatedItem) => {
                      const updateInOriginalOutline = (items: OutlineItemType[], updated: OutlineItemType): OutlineItemType[] => {
                        return items.map(i => {
                          if (i.id === updated.id) return updated;
                          if (i.children) return { ...i, children: updateInOriginalOutline(i.children, updated) };
                          return i;
                        });
                      };
                      const newOriginalOutline = updateInOriginalOutline(outline, updatedItem);
                      onOutlineChange(newOriginalOutline);
                      notifyMinimapChange();
                    }}
                    onDragStart={handleDragStart}
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                    onDragEnd={handleDragEnd}
                    isDraggedOver={dragOverTarget?.id === item.id}
                    dragOverPosition={dragOverTarget?.id === item.id ? dragOverTarget.position : undefined}
                    dragOverTarget={dragOverTarget}
                    onAddSibling={handleAddSibling}
                    onAddChild={() => onAddItem(item.id)}
                    allNotes={notes}
                    onAddNote={(outlineItemId, noteType) => {
                      onNoteCreate(outlineItemId, { type: noteType });
                    }}
                    onLinkNoteToOutlineItem={onLinkNoteToOutlineItem}
                    onDeleteOutlineItemWithNotes={(itemId) => {
                      setOutlineItemToDeleteId(itemId);
                      setIsConfirmDeleteDialogOpen(true);
                    }}
                    onNoteDelete={onNoteDelete}
                    onNoteDuplicate={onNoteDuplicate}
                    onNoteUpdate={onNoteUpdate}
                    onFileUpload={onFileUpload}
                    allOutlineItems={getFlatOutlineItems(outline)}
                    isCurrentSearchResult={
                      currentSearchResultIndex >= 0 &&
                      searchResults[currentSearchResultIndex]?.item.id === item.id
                    }
                    searchTerm={outlineSearchTerm}
                    // Note drag and drop props
                    onNoteDragStart={handleNoteDragStart}
                    onNoteDragOver={handleNoteDragOver}
                    onNoteDrop={handleNoteDrop}
                    onNoteDragEnd={handleNoteDragEnd}
                    noteDragOverTarget={noteDragOverTarget}
                    draggedNote={draggedNote}
                  />
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full py-12 text-center">
                {outlineSearchTerm ? (
                  <>
                    <i className="ri-search-line text-4xl text-muted-foreground mb-4"></i>
                    <h3 className="text-lg font-medium text-foreground mb-2">No results found</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Try adjusting your search terms or{' '}
                      <button
                        onClick={() => setOutlineSearchTerm('')}
                        className="text-primary hover:underline"
                      >
                        clear the search
                      </button>
                    </p>
                  </>
                ) : (
                  <>
                    <i className="ri-file-list-3-line text-4xl text-muted-foreground mb-4"></i>
                    <h3 className="text-lg font-medium text-foreground mb-2">No outline yet</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Start building your document structure by adding sections.
                    </p>
                    <Button onClick={() => onAddItem()} variant="outline">
                      <i className="ri-add-line mr-2"></i>
                      Create your first section
                    </Button>
                  </>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Minimap */}
        <div className="w-24 border-l border-border bg-muted/30">
          <NewMinimap
            contentRef={mainContentRef}
            width={80}
            height="100%"
            highlightedItemId={hoveredItemPath.length > 0 ? hoveredItemPath[hoveredItemPath.length - 1] : null}
            activeParentItemIds={hoveredItemPath.slice(0, -1)}
          />
        </div>
      </div>

      {/* Confirm delete modal */}
      <ConfirmModal
        isOpen={isConfirmDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        title="Delete Outline Item"
        description="Are you sure you want to delete this outline item and all its associated notes? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
      />
    </div>
  );
}
