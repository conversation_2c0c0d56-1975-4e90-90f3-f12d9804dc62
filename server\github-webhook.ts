// routes/github-webhook.ts
import "dotenv/config";
import express from 'express';
import crypto from 'crypto';
import execSh from 'exec-sh';

const exec = execSh.promise;
export const githubWebhookRouter = express.Router();

// Use express.raw ONLY for this route
githubWebhookRouter.post(
  '/',
  express.raw({ type: 'application/json' }),
  async (req, res) => {
    try {
      const sig = (req.get('X-Hub-Signature-256') || '').trim();

      // Sanity check that req.body is a Buffer
      if (!(req.body instanceof Buffer)) {
        console.error('❌ Webhook body is not a Buffer');
        return res.status(400).send('Invalid body type');
      }

      const hmac = crypto
        .createHmac('sha256', process.env.GITHUB_WEBHOOK_SECRET!)
        .update(req.body)
        .digest('hex');

      if (sig !== `sha256=${hmac}`) {
        console.warn('❌ Webhook signature mismatch');
        return res.status(401).send('Bad signature');
      }

      res.status(204).end(); // Acknowledge webhook

      // --- Deployment sequence ---
      console.log('📥 Pulling, installing, restarting…');
      await exec(`cd ~/www`);
      await exec(`git pull --ff-only`);
      await exec(`npm install`);
      await exec(`npm run build`);
      await exec(`pm2 restart inspire`);
      console.log('🚀 Deployment finished');
    } catch (err) {
      console.error('❌ Deployment failed:', err);
    }
  }
);
