// routes/github-webhook.ts
import "dotenv/config";
import express from 'express';
import crypto from 'crypto';
import execSh from 'exec-sh';

const exec = execSh.promise;
export const githubWebhookRouter = express.Router();

// Use express.raw ONLY for this route
githubWebhookRouter.post(
  '/',
  express.raw({ type: 'application/json' }),
  async (req, res) => {
    try {
      const sig = (req.get('X-Hub-Signature-256') || '').trim();

      // Sanity check that req.body is a Buffer
      if (!(req.body instanceof Buffer)) {
        console.error('❌ Webhook body is not a Buffer');
        return res.status(400).send('Invalid body type');
      }

      const hmac = crypto
        .createHmac('sha256', process.env.GITHUB_WEBHOOK_SECRET!)
        .update(req.body)
        .digest('hex');

      if (sig !== `sha256=${hmac}`) {
        console.warn('❌ Webhook signature mismatch');
        return res.status(401).send('Bad signature');
      }

      // Parse the webhook payload
      const payload = JSON.parse(req.body.toString());
      const eventType = req.get('X-GitHub-Event');

      // Check if this is a push event to the main branch
      if (eventType !== 'push') {
        console.log(`ℹ️ Ignoring ${eventType} event (not a push)`);
        return res.status(204).end();
      }

      // Check if the push is to the main branch
      const ref = payload.ref;
      if (ref !== 'refs/heads/main') {
        console.log(`ℹ️ Ignoring push to ${ref} (not main branch)`);
        return res.status(204).end();
      }

      // Optional: Check if this is a merge commit (has multiple parents)
      const headCommit = payload.head_commit;
      if (headCommit && headCommit.parents && headCommit.parents.length > 1) {
        console.log(`🔀 Detected merge commit: ${headCommit.message}`);
      } else {
        console.log(`📝 Regular commit to main: ${headCommit?.message || 'Unknown commit'}`);
        // Uncomment the next two lines if you want to ONLY deploy on merge commits
        console.log('ℹ️ Ignoring non-merge commit');
        return res.status(204).end();
      }

      res.status(204).end(); // Acknowledge webhook

      // --- Deployment sequence ---
      console.log('📥 Pulling, installing, restarting…');
      await exec(`cd ~/www`);
      await exec(`git pull --ff-only`);
      await exec(`npm install`);
      await exec(`npm run build`);
      await exec(`pm2 restart inspire`);
      console.log('🚀 Deployment finished');
    } catch (err) {
      console.error('❌ Deployment failed:', err);
    }
  }
);
