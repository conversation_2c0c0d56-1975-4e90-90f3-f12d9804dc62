import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { User, SystemSetting } from "@shared/schema";
import { useToast } from "@/hooks/use-toast";

// Get admin analytics (admin only)
export function useAdminAnalytics() {
  return useQuery({
    queryKey: ['/api/admin/analytics'],
    enabled: true,
    refetchInterval: 30000, // Refresh every 30 seconds
  });
}

// Get all users (admin only)
export function useAdminUsers() {
  return useQuery({
    queryKey: ['/api/admin/users'],
    enabled: true, // Will be protected by server-side auth
  });
}

// Get all system settings (admin only)
export function useAdminSettings() {
  return useQuery({
    queryKey: ['/api/admin/settings'],
    enabled: true,
  });
}

// Create new user (admin only)
export function useCreateUser() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (userData: { username: string; password: string; isPremium?: boolean }) => {
      const res = await apiRequest("POST", "/api/admin/users", userData);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
      toast({
        title: "User created",
        description: "New user account has been created successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Failed to create user",
        description: error.message || "An error occurred while creating the user",
        variant: "destructive",
      });
    },
  });
}

// Update user status (admin only)
export function useUpdateUserStatus() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ userId, isActive }: { userId: number; isActive: boolean }) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${userId}/status`, { isActive });
      return await res.json();
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
      toast({
        title: "User status updated",
        description: `User account has been ${variables.isActive ? 'activated' : 'deactivated'}`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Failed to update user status",
        description: error.message || "An error occurred while updating the user status",
        variant: "destructive",
      });
    },
  });
}

// Update user premium status (admin only)
export function useUpdateUserPremium() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ userId, isPremium }: { userId: number; isPremium: boolean }) => {
      const res = await apiRequest("PATCH", `/api/admin/users/${userId}/premium`, { isPremium });
      return await res.json();
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
      toast({
        title: "Premium status updated",
        description: `User ${variables.isPremium ? 'upgraded to' : 'downgraded from'} premium`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Failed to update premium status",
        description: error.message || "An error occurred while updating the premium status",
        variant: "destructive",
      });
    },
  });
}

// Update system setting (admin only)
export function useUpdateSystemSetting() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ key, value, description }: { key: string; value: string; description?: string }) => {
      const res = await apiRequest("PUT", `/api/admin/settings/${key}`, { value, description });
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/settings'] });
      toast({
        title: "Setting updated",
        description: "System setting has been updated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Failed to update setting",
        description: error.message || "An error occurred while updating the setting",
        variant: "destructive",
      });
    },
  });
}
