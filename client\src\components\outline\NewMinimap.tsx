import React, { Component, RefObject } from 'react';
import styles from './NewMinimap.module.css'; // Adjusted import path

// Define a type for the data we'll extract for each outline item
interface TextSegment {
  text: string;
  hasExplicitNewlines: boolean; // True if the original segment string contained one or more '\n'
}

export enum IndividualNoteContentType { // Exporting if it might be useful elsewhere, or keep local
  Text = 'text',
  Image = 'image',
  Video = 'video',
  File = 'file',
  GenericPlaceholder = 'genericplaceholder',
  CollapsedPreview = 'collapsedpreview', // For the snippet text of a collapsed individual note
}

interface ExpandedNoteDisplayData {
  title: TextSegment; // Title of the individual note
  isExpanded: boolean; // Was the individual note itself expanded?
  contentType: IndividualNoteContentType;
  // For CollapsedPreview, contentDetails is TextSegment of the snippet.
  // For Text (expanded), contentDetails is TextSegment of the processed prose.
  // For Image, Video, File (expanded), contentDetails contains the actual source/preview data.
  contentDetails: TextSegment | string | {
    type: 'image' | 'video' | 'file';
    src?: string;
    width?: number;
    height?: number;
    fileName?: string;
    fileType?: string;
  } | null;
  actualHeightInMainPanel: number | null; // Actual offsetHeight of the expanded content in the main panel
}

interface OutlineItemData {
  id: string; // Internal ID for minimap (e.g., "item-0")
  originalId: string; // Actual ID from the outline data
  title: TextSegment;
  nestingLevel: number;
  itemNumber: string | null;
  hasNotes: boolean;
  isNotesExpanded: boolean;
  notePreview: TextSegment | null; // Preview for the entire collapsed notes section of an outline item
  expandedNotesText: ExpandedNoteDisplayData[] | null; // Details for each individual note when main section is open
  mainPanelOffsetTop: number;    // offsetTop of the item in the main content panel
  mainPanelOffsetHeight: number; // offsetHeight of the item in the main content panel
}

interface MinimapProps {
  contentRef: RefObject<HTMLElement>;
  width?: number;
  height?: string | number;
  highlightedItemId?: string | null; // ID of the item hovered in the main outline
  activeParentItemIds?: string[]; // IDs of the parents of the hovered item
}

interface MinimapState {
  scrollPercent: number;
  outlineItemsData: OutlineItemData[];
  indicatorTop: number;
  indicatorHeight: number;
  isDraggingIndicator: boolean;
  loadedImages: Map<string, HTMLImageElement>;
  loadedVideos: Map<string, HTMLVideoElement>;
}

class NewMinimap extends Component<MinimapProps, MinimapState> {
  private refMinimap = React.createRef<HTMLDivElement>();
  private refCanvas = React.createRef<HTMLCanvasElement>();
  private canvasCtx: CanvasRenderingContext2D | null = null;

  private readonly MINIMAP_FONT_FAMILY = 'Arial, sans-serif';
  private readonly MAIN_PANEL_LINE_HEIGHT_EQUIVALENT_FOR_MINIMAP = 20; // Approx. px height of one line in main panel

  // Preload images and videos for synchronous rendering
  private preloadMediaContent(outlineItemsData: OutlineItemData[]) {
    const newLoadedImages = new Map(this.state.loadedImages);
    const newLoadedVideos = new Map(this.state.loadedVideos);
    let hasNewMedia = false;

    outlineItemsData.forEach(item => {
      if (item.expandedNotesText) {
        item.expandedNotesText.forEach(noteData => {
          if (noteData.contentDetails && typeof noteData.contentDetails === 'object' && 'type' in noteData.contentDetails) {
            const mediaDetails = noteData.contentDetails as { type: string; src: string };

            if (mediaDetails.type === 'image' && mediaDetails.src && !newLoadedImages.has(mediaDetails.src)) {
              const img = new Image();
              img.crossOrigin = 'anonymous';
              img.onload = () => {
                this.setState(prevState => ({
                  loadedImages: new Map(prevState.loadedImages).set(mediaDetails.src, img)
                }), () => {
                  // Trigger a redraw when image loads
                  this.drawMinimap();
                });
              };
              img.src = mediaDetails.src;
              hasNewMedia = true;
            } else if (mediaDetails.type === 'video' && mediaDetails.src && !newLoadedVideos.has(mediaDetails.src)) {
              const video = document.createElement('video');
              video.crossOrigin = 'anonymous';
              video.muted = true;
              video.currentTime = 1;
              video.onloadeddata = () => {
                this.setState(prevState => ({
                  loadedVideos: new Map(prevState.loadedVideos).set(mediaDetails.src, video)
                }), () => {
                  // Trigger a redraw when video loads
                  this.drawMinimap();
                });
              };
              video.src = mediaDetails.src;
              hasNewMedia = true;
            }
          }
        });
      }
    });
  }

  private observer: MutationObserver | null = null;
  private scrollTimeout: NodeJS.Timeout | null = null;

  // Properties for drag handling
  private dragStartMouseY: number = 0;
  private dragStartScrollTop: number = 0;

  static defaultProps = {
    width: 80,
    height: '100%',
  };

  constructor(props: MinimapProps) {
    super(props);
    this.state = {
      scrollPercent: 0,
      outlineItemsData: [],
      indicatorTop: 0,
      indicatorHeight: 0.1, // Initial small height until calculated
      isDraggingIndicator: false,
      loadedImages: new Map(),
      loadedVideos: new Map(),
    };

    // Bind methods in constructor
    this.handleMouseDownOnMinimap = this.handleMouseDownOnMinimap.bind(this);
    this.handleGlobalMouseMove = this.handleGlobalMouseMove.bind(this);
    this.handleGlobalMouseUp = this.handleGlobalMouseUp.bind(this);
  }

  componentDidMount() {
    window.addEventListener('resize', this.handleResize);
    this.initializeMinimap();
  }

  initializeMinimap = () => {
    if (this.props.contentRef.current && this.refMinimap.current && this.refCanvas.current) {
      this.canvasCtx = this.refCanvas.current.getContext('2d');
      this.props.contentRef.current.addEventListener('scroll', this.handleContentScroll);

      this.setCanvasDimensions();
      this.extractAndDrawMinimap();

      this.setupMutationObserver();
      this.updateScrollPercent();
    }
  }

  componentWillUnmount() {
    if (this.props.contentRef.current) {
      this.props.contentRef.current.removeEventListener('scroll', this.handleContentScroll);
    }
    window.removeEventListener('resize', this.handleResize);
    // Clean up global listeners if component unmounts during a drag
    window.removeEventListener('mousemove', this.handleGlobalMouseMove);
    window.removeEventListener('mouseup', this.handleGlobalMouseUp);

    if (this.observer) {
      this.observer.disconnect();
    }
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
  }

  handleMouseDownOnMinimap(event: React.MouseEvent<HTMLDivElement>) {
    if (!this.refMinimap.current || !this.props.contentRef.current) return;

    const minimapRect = this.refMinimap.current.getBoundingClientRect();
    const offsetY = event.clientY - minimapRect.top; // Use clientY and rect.top for consistency with global mousemove

    const logicalHeight = this.refMinimap.current.clientHeight;
    const { indicatorTop, indicatorHeight } = this.state; // These are ratios

    const indicatorActualTop = indicatorTop * logicalHeight;
    const indicatorActualHeight = indicatorHeight * logicalHeight;

    // Check if click is on the indicator
    if (offsetY >= indicatorActualTop && offsetY <= indicatorActualTop + indicatorActualHeight) {
      event.preventDefault();
      this.dragStartMouseY = event.clientY;
      this.dragStartScrollTop = this.props.contentRef.current.scrollTop;
      this.setState({ isDraggingIndicator: true });

      window.addEventListener('mousemove', this.handleGlobalMouseMove);
      window.addEventListener('mouseup', this.handleGlobalMouseUp);
    } else {
      // Click was not on indicator, perform click-to-scroll
      const clickYRatio = offsetY / logicalHeight;
      const contentEl = this.props.contentRef.current;
      if (contentEl.scrollHeight > contentEl.clientHeight) {
          // Calculate target scrollTop based on the click ratio relative to the scrollable range
          const targetScrollTop = clickYRatio * contentEl.scrollHeight - (contentEl.clientHeight / 2);
          contentEl.scrollTo({ top: Math.max(0, Math.min(targetScrollTop, contentEl.scrollHeight - contentEl.clientHeight)), behavior: 'smooth' });
      } else {
          contentEl.scrollTo({ top: 0, behavior: 'smooth' });
      }
    }
  }

  handleGlobalMouseMove(event: MouseEvent) {
    if (!this.state.isDraggingIndicator || !this.props.contentRef.current || !this.refMinimap.current) {
      return;
    }
    event.preventDefault(); // Prevent text selection, etc.

    const mouseDeltaY = event.clientY - this.dragStartMouseY;

    const minimapLogicalHeight = this.refMinimap.current.clientHeight;
    if (minimapLogicalHeight === 0) return;

    const contentEl = this.props.contentRef.current;
    const contentScrollHeight = contentEl.scrollHeight;
    const contentClientHeight = contentEl.clientHeight;

    // How much one pixel of minimap drag corresponds to in content scroll pixels
    const scrollRatio = contentScrollHeight / minimapLogicalHeight;
    const scrollDelta = mouseDeltaY * scrollRatio;

    let newScrollTop = this.dragStartScrollTop + scrollDelta;
    newScrollTop = Math.max(0, Math.min(newScrollTop, contentScrollHeight - contentClientHeight)); // Clamp

    contentEl.scrollTop = newScrollTop;
    // The scroll event on contentEl will trigger indicator update via handleContentScroll -> updateScrollPercent
  }

  handleGlobalMouseUp(event: MouseEvent) {
    if (this.state.isDraggingIndicator) {
      this.setState({ isDraggingIndicator: false });
      window.removeEventListener('mousemove', this.handleGlobalMouseMove);
      window.removeEventListener('mouseup', this.handleGlobalMouseUp);
    }
  }

  setCanvasDimensions = () => {
    if (this.refCanvas.current && this.refMinimap.current) {
      const minimapDiv = this.refMinimap.current;
      const canvas = this.refCanvas.current;
      const dpr = window.devicePixelRatio || 1;

      canvas.width = minimapDiv.clientWidth * dpr;
      canvas.height = minimapDiv.clientHeight * dpr;
      canvas.style.width = `${minimapDiv.clientWidth}px`;
      canvas.style.height = `${minimapDiv.clientHeight}px`;

      this.canvasCtx?.scale(dpr, dpr);
    }
  }

  extractAndDrawMinimap = () => {
    const { contentRef } = this.props;

    if (!contentRef.current) {
      this.setState({ outlineItemsData: [] }, () => this.drawMinimap());
      return;
    }

    const sourceContainer = contentRef.current.querySelector('.outline-list-container');
    if (!sourceContainer) {
      console.warn("🗺️ Minimap: '.outline-list-container' not found for data extraction.");
      this.setState({ outlineItemsData: [] }, () => this.drawMinimap());
      return;
    }

    const sourceOutlineItemElements = sourceContainer.querySelectorAll(".outline-item");
    const newOutlineItemsData: OutlineItemData[] = [];
    // console.log(`[Minimap Extract Debug] Found ${sourceOutlineItemElements.length} .outline-item elements.`); // Reduced logging

    sourceOutlineItemElements.forEach((itemEl, index) => {
      if (itemEl instanceof HTMLElement) {
        const titleInput = itemEl.querySelector('.outline-item-title') as HTMLInputElement;
        const titleStr = titleInput ? titleInput.value : `Item ${index + 1}`;
        const titleSegment: TextSegment = {
            text: titleStr || '',
            hasExplicitNewlines: (titleStr || '').includes('\n')
        };

        const numberEl = itemEl.querySelector('.outline-item-number') as HTMLElement;
        const itemNumber = numberEl ? (numberEl.textContent ? numberEl.textContent.trim() : null) : null;


        const notesSectionWrapperEl = itemEl.querySelector('.outline-item-notes-preview-wrapper');
        const mainNotesTrigger = notesSectionWrapperEl ? notesSectionWrapperEl.querySelector('button[data-state]') : null;

        const hasNotes = !!mainNotesTrigger;
        const isMainNotesSectionExpanded = mainNotesTrigger ? mainNotesTrigger.getAttribute('data-state') === 'open' : false;

        let notePreviewSegment: TextSegment | null = null;
        let expandedNoteSegments: TextSegment[] | null = null;

        if (hasNotes) {
            if (!isMainNotesSectionExpanded) {
                if (mainNotesTrigger && mainNotesTrigger.textContent) {
                    const previewStr = mainNotesTrigger.textContent.trim().replace(/\s+/g, ' ');
                    notePreviewSegment = {
                        text: previewStr,
                        hasExplicitNewlines: (previewStr || '').includes('\n')
                    };
                }
            } else { // isMainNotesSectionExpanded is true
                let mainNotesContentEl: Element | null = null;
                if (notesSectionWrapperEl) {
                    mainNotesContentEl = notesSectionWrapperEl.querySelector('div[id^="radix-"][data-state="open"]');
                    if (!mainNotesContentEl) {
                        mainNotesContentEl = notesSectionWrapperEl.querySelector('div[data-state="open"]:not(button)');
                    }
                }

                if (mainNotesContentEl) {
                    const individualNoteWrappers = mainNotesContentEl.querySelectorAll('div.group.relative');
                    expandedNoteSegments = [];

                    individualNoteWrappers.forEach((noteWrapper) => {
                        const clickableArea = noteWrapper.querySelector('div[aria-expanded][data-state]');
                        let isIndividualNoteActuallyExpanded = false;
                        if (clickableArea) {
                            isIndividualNoteActuallyExpanded = clickableArea.getAttribute('aria-expanded') === 'true' || clickableArea.getAttribute('data-state') === 'open';
                        } else {
                            const altTrigger = noteWrapper.querySelector('.flex.items-start.space-x-1\\.5.cursor-pointer');
                            if (altTrigger) isIndividualNoteActuallyExpanded = altTrigger.getAttribute('data-state') === 'open';
                        }

                        const noteTitleEl = noteWrapper.querySelector('p.font-medium.truncate');
                        const noteTitleStr = noteTitleEl?.textContent?.trim() || "[Untitled Note]";
                        const noteTitleSeg: TextSegment = { text: noteTitleStr, hasExplicitNewlines: (noteTitleStr || '').includes('\n') };

                        let contentType: IndividualNoteContentType = IndividualNoteContentType.GenericPlaceholder;
                        let contentDetails: TextSegment | string | null = null;
                        let actualHeightInMainPanel: number | null = null;

                        if (isIndividualNoteActuallyExpanded) {
                            const individualNoteOpenContentArea = noteWrapper.querySelector('div[data-state="open"][id^="radix-"]');
                            let contentElementForHeightMeasurement: HTMLElement | null = null;

                            if (individualNoteOpenContentArea) {
                                const proseEditorEl = individualNoteOpenContentArea.querySelector('div.prose[contenteditable="true"]') as HTMLElement;
                                const imgEl = individualNoteOpenContentArea.querySelector('img') as HTMLElement;
                                const videoEl = individualNoteOpenContentArea.querySelector('video') as HTMLElement;
                                const iframeEl = individualNoteOpenContentArea.querySelector('iframe') as HTMLElement;
                                // Use .WysiwygNoteEditor or .cm-editor as a wrapper for height, as .cm-content might be too specific or small initially
                                const cmEditorWrapperEl = individualNoteOpenContentArea.querySelector('.WysiwygNoteEditor') as HTMLElement || individualNoteOpenContentArea.querySelector('.cm-editor') as HTMLElement;


                                if (proseEditorEl) {
                                    contentType = IndividualNoteContentType.Text;
                                    contentElementForHeightMeasurement = proseEditorEl;
                                    let processedText = "";
                                    const blockElements = ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li', 'blockquote', 'div'];
                                    proseEditorEl.childNodes.forEach(node => {
                                        if (node.nodeType === Node.ELEMENT_NODE) {
                                            const el = node as Element;
                                            const currentText = (el.textContent || "").trim();
                                            if (blockElements.includes(el.tagName.toLowerCase())) {
                                                if (processedText !== "" && !processedText.endsWith("\n")) processedText += "\n";
                                                processedText += currentText;
                                                if (el.nextSibling && blockElements.includes((el.nextSibling as Element).tagName?.toLowerCase())) processedText += "\n";
                                            } else {
                                                processedText += currentText + " ";
                                            }
                                        } else if (node.nodeType === Node.TEXT_NODE) {
                                            processedText += (node.textContent || "");
                                        }
                                    });
                                    let finalProcessedText = processedText.trim().replace(/\n\s*\n/g, '\n');
                                    contentDetails = { text: finalProcessedText || "", hasExplicitNewlines: (finalProcessedText || '').includes('\n') };
                                } else if (cmEditorWrapperEl) {
                                    contentType = IndividualNoteContentType.Text;
                                    contentElementForHeightMeasurement = cmEditorWrapperEl;
                                    const cmContentEl = cmEditorWrapperEl.querySelector('.cm-content');
                                    let editorText = "";
                                    if (cmContentEl) {
                                        cmContentEl.querySelectorAll('.cm-line').forEach(line => editorText += (line.textContent || "") + "\n");
                                    }
                                    const finalText = editorText.trim();
                                    contentDetails = { text: finalText, hasExplicitNewlines: (finalText || '').includes('\n') };
                                } else if (imgEl) {
                                    contentType = IndividualNoteContentType.Image;
                                    contentElementForHeightMeasurement = imgEl;
                                    const imageElement = imgEl as HTMLImageElement;
                                    contentDetails = {
                                        type: 'image',
                                        src: imageElement.getAttribute('src') || '',
                                        width: imageElement.naturalWidth || imageElement.width || 0,
                                        height: imageElement.naturalHeight || imageElement.height || 0
                                    } as any;
                                } else if (videoEl) {
                                    contentType = IndividualNoteContentType.Video;
                                    contentElementForHeightMeasurement = videoEl;
                                    const videoElement = videoEl as HTMLVideoElement;
                                    contentDetails = {
                                        type: 'video',
                                        src: videoElement.getAttribute('src') || videoElement.querySelector('source')?.getAttribute('src') || '',
                                        width: videoElement.videoWidth || videoElement.width || 0,
                                        height: videoElement.videoHeight || videoElement.height || 0
                                    } as any;
                                } else if (iframeEl) {
                                    contentType = IndividualNoteContentType.File;
                                    contentElementForHeightMeasurement = iframeEl;
                                    const src = iframeEl.getAttribute('src') || '';
                                    contentDetails = {
                                        type: 'file',
                                        src: src,
                                        fileName: src.split('/').pop() || 'Unknown File',
                                        fileType: src.toLowerCase().includes('.pdf') ? 'PDF' : 'File'
                                    } as any;
                                } else {
                                    contentType = IndividualNoteContentType.GenericPlaceholder;
                                    contentElementForHeightMeasurement = individualNoteOpenContentArea as HTMLElement; // Fallback to the open area itself
                                    contentDetails = "[Non-text content or empty]";
                                }
                                if (contentElementForHeightMeasurement) {
                                    actualHeightInMainPanel = contentElementForHeightMeasurement.offsetHeight;
                                } else if (individualNoteOpenContentArea) { // Fallback if specific content element wasn't found but area is open
                                    actualHeightInMainPanel = (individualNoteOpenContentArea as HTMLElement).offsetHeight;
                                }
                            } else { // individualNoteOpenContentArea not found, but isIndividualNoteActuallyExpanded is true
                                contentType = IndividualNoteContentType.GenericPlaceholder;
                                contentDetails = "[Unknown Expanded Content Structure]";
                                // Try to get height from noteWrapper itself or a generic child if possible
                                const genericContentHolder = noteWrapper.querySelector('div.mt-1') as HTMLElement;
                                if (genericContentHolder) {
                                    actualHeightInMainPanel = genericContentHolder.offsetHeight;
                                }
                            }
                        } else { // Note is collapsed
                            contentType = IndividualNoteContentType.CollapsedPreview;
                            const snippetEl = noteWrapper.querySelector('p.text-muted-foreground.whitespace-normal.break-words');
                            if (snippetEl) {
                                const snippetStr = (snippetEl.textContent || "").trim();
                                if (snippetStr) {
                                   contentDetails = { text: snippetStr, hasExplicitNewlines: (snippetStr || '').includes('\n') };
                                } else if (noteWrapper.querySelector('img')) {
                                    contentDetails = { text: "[Image]", hasExplicitNewlines: false };
                                } else if (noteWrapper.querySelector('video')) {
                                    contentDetails = { text: "[Video]", hasExplicitNewlines: false };
                                } else if (noteWrapper.querySelector('i.ri-file-3-fill, i.ri-file-pdf-fill')) {
                                    contentDetails = { text: "[File]", hasExplicitNewlines: false };
                                } else {
                                    contentDetails = { text: "", hasExplicitNewlines: false };
                                }
                            } else if (noteWrapper.querySelector('img')) {
                                contentDetails = { text: "[Image]", hasExplicitNewlines: false };
                            } else if (noteWrapper.querySelector('video')) {
                                contentDetails = { text: "[Video]", hasExplicitNewlines: false };
                            } else if (noteWrapper.querySelector('i.ri-file-3-fill, i.ri-file-pdf-fill')) {
                                contentDetails = { text: "[File]", hasExplicitNewlines: false };
                            } else {
                                contentDetails = { text: "", hasExplicitNewlines: false };
                            }
                        }
                        expandedNoteSegments!.push({
                            title: noteTitleSeg,
                            isExpanded: isIndividualNoteActuallyExpanded,
                            contentType: contentType,
                            contentDetails: contentDetails,
                            actualHeightInMainPanel: actualHeightInMainPanel
                        });
                    });
                }
            }
        }

        let calculatedNestingLevel = 0;
        let parentWalker = itemEl.parentElement;
        while (parentWalker && parentWalker !== sourceContainer) {
            if (parentWalker.tagName === 'DIV' && parentWalker.dataset.childContainer === 'true') {
                calculatedNestingLevel++;
            }
            parentWalker = parentWalker.parentElement;
        }
        const nestingLevel = calculatedNestingLevel;
        const originalItemId = itemEl.dataset.id || `fallback-original-id-${index}`;

        newOutlineItemsData.push({
          id: `item-${index}`,
          originalId: originalItemId,
          title: titleSegment,
          nestingLevel: nestingLevel,
          itemNumber: itemNumber,
          hasNotes: hasNotes,
          isNotesExpanded: isMainNotesSectionExpanded,
          notePreview: notePreviewSegment,
          expandedNotesText: expandedNoteSegments,
          mainPanelOffsetTop: itemEl.offsetTop,
          mainPanelOffsetHeight: itemEl.offsetHeight,
        });
      }
    });

    this.setState({ outlineItemsData: newOutlineItemsData }, () => {
       this.preloadMediaContent(newOutlineItemsData);
       this.updateScrollPercent();
    });
  }

  private wrapText(
    ctx: CanvasRenderingContext2D,
    text: string,
    maxWidth: number
  ): string[] {
    if (!text) return [];
    const lines: string[] = [];
    const paragraphs = text.split('\n');

    for (const paragraph of paragraphs) {
      if (paragraph.trim() === "" && paragraphs.length > 1) {
        lines.push("");
        continue;
      }
      if (!paragraph.trim()) continue;

      const words = paragraph.split(' ');
      let currentLine = "";
      for (let i = 0; i < words.length; i++) {
        const word = words[i];
        const testLine = currentLine ? currentLine + " " + word : word;
        const metrics = ctx.measureText(testLine);
        if (metrics.width > maxWidth && currentLine) {
          lines.push(currentLine);
          currentLine = word;
        } else {
          currentLine = testLine;
        }
      }
      if (currentLine) {
        lines.push(currentLine);
      }
    }
    return lines;
  }

  private isDrawingOffCanvas(
    currentYUnscaled: number,
    yScaleFactor: number,
    logicalHeight: number,
    textLineHeight: number,
    totalMinimapRenderedHeightIfUnscaled: number,
    isMultiLineBlock: boolean = false
  ): boolean {
    if (yScaleFactor >= 1 || totalMinimapRenderedHeightIfUnscaled <= logicalHeight) {
      return false;
    }
    const currentYScaled = currentYUnscaled * yScaleFactor;
    const scaledTextLineHeight = textLineHeight * yScaleFactor;
    if (currentYScaled >= logicalHeight) return true;
    if (isMultiLineBlock && currentYScaled + scaledTextLineHeight > logicalHeight && currentYScaled > logicalHeight - scaledTextLineHeight * 0.5) return true;
    if (currentYScaled + scaledTextLineHeight > logicalHeight) {
        if ((logicalHeight - currentYScaled) < (scaledTextLineHeight * 0.75)) return true;
    }
    return false;
  }

  private drawPlaceholderBox(
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    width: number,
    numLines: number,
    lineHeight: number,
    text: string,
    fillStyle: string
  ): void {
    const boxHeight = numLines * lineHeight;
    const padding = 2;
    const originalFillStyle = ctx.fillStyle;
    ctx.fillStyle = fillStyle;
    ctx.fillRect(x, y, width, boxHeight);
    ctx.fillStyle = originalFillStyle;
    const textX = x + width / 2;
    const textY = y + boxHeight / 2;
    const originalTextAlign = ctx.textAlign;
    const originalTextBaseline = ctx.textBaseline;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    const maxTextWidth = width - padding * 2;
    let displayText = text;
    if (ctx.measureText(displayText).width > maxTextWidth) {
      while (ctx.measureText(displayText + '...').width > maxTextWidth && displayText.length > 0) {
        displayText = displayText.substring(0, displayText.length - 1);
      }
      if (displayText.length > 0) displayText += '...';
      else displayText = "";
    }
    if (displayText) ctx.fillText(displayText, textX, textY, maxTextWidth);
    ctx.textAlign = originalTextAlign;
    ctx.textBaseline = originalTextBaseline;
  }

  drawMinimap = () => {
    if (!this.canvasCtx || !this.refCanvas.current) return;
    const ctx = this.canvasCtx;
    const canvas = this.refCanvas.current;
    const items = this.state.outlineItemsData;
    const logicalWidth = parseFloat(canvas.style.width || '0');
    const logicalHeight = parseFloat(canvas.style.height || '0');

    ctx.save();
    ctx.setTransform(1, 0, 0, 1, 0, 0);
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.restore();

    if (items.length === 0 || logicalHeight === 0) return;

    const fontSize = 6;
    const textLineHeight = fontSize + 2;
    ctx.font = `${fontSize}px ${this.MINIMAP_FONT_FAMILY}`;
    const defaultXOffset = logicalWidth * 0.05;
    const indentSize = logicalWidth * 0.04;
    const maxNestingForIndentCalc = 5;

    let totalMinimapRenderedHeightIfUnscaled = 0;
    items.forEach((item) => {
      totalMinimapRenderedHeightIfUnscaled += this.calculateUnscaledMinimapItemHeight(
        item, ctx, logicalWidth, fontSize, textLineHeight, defaultXOffset, indentSize, maxNestingForIndentCalc
      );
    });

    const FIXED_ITEM_SCALE = 0.15;
    let yScaleFactor = FIXED_ITEM_SCALE;


    ctx.save();
    ctx.scale(1, yScaleFactor);
    ctx.font = `${fontSize}px ${this.MINIMAP_FONT_FAMILY}`;
    ctx.textBaseline = 'top';

    const baseFgHslString = getComputedStyle(canvas).getPropertyValue('--foreground-hsl').trim();
    let fgH = 240, fgS = 10, fgL = 4;
    if (baseFgHslString) {
        const parts = baseFgHslString.match(/(\d+(\.?\d*)?)/g);
        if (parts && parts.length >=3) {
            fgH = parseFloat(parts[0]); fgS = parseFloat(parts[1]); fgL = parseFloat(parts[2]);
        }
    }
    const fgColor = `hsl(${fgH}, ${fgS}%, ${fgL}%)`;

    const primaryColorHslString = getComputedStyle(canvas).getPropertyValue('--primary').trim();
    let primaryH = 214, primaryS = 100, primaryL = 60;
    if (primaryColorHslString) {
        const parts = primaryColorHslString.match(/(\d+(\.?\d*)?)/g);
        if (parts && parts.length >= 3) {
            primaryH = parseFloat(parts[0]);
            primaryS = parseFloat(parts[1]);
            primaryL = parseFloat(parts[2]);
        }
    }
    const primaryHighlightColor = `hsla(${primaryH}, ${primaryS}%, ${primaryL}%, 0.35)`;
    const parentHighlightColor = `hsla(${primaryH}, ${primaryS}%, ${primaryL}%, 0.15)`;

    let currentYUnscaled = 0;
    items.forEach((item) => {
        const itemStartYUnscaled = currentYUnscaled;
        if (currentYUnscaled * yScaleFactor >= logicalHeight && yScaleFactor < 1 && totalMinimapRenderedHeightIfUnscaled > logicalHeight) return;

        const effectiveNesting = Math.min(item.nestingLevel, maxNestingForIndentCalc);
        let itemContentX = defaultXOffset + (effectiveNesting * indentSize);
        const availableWidthForLineContent = logicalWidth - itemContentX - (logicalWidth * 0.05);
        const originalFillStyle = fgColor;
        ctx.fillStyle = originalFillStyle;

        let itemHeightUnscaled = this.calculateUnscaledMinimapItemHeight(item, ctx, logicalWidth, fontSize, textLineHeight, defaultXOffset, indentSize, maxNestingForIndentCalc);

        const { highlightedItemId, activeParentItemIds } = this.props;
        if (highlightedItemId === item.originalId) {
            ctx.fillStyle = primaryHighlightColor;
            ctx.fillRect(0, itemStartYUnscaled, logicalWidth, itemHeightUnscaled);
        } else if (activeParentItemIds && activeParentItemIds.includes(item.originalId)) {
            ctx.fillStyle = parentHighlightColor;
            ctx.fillRect(0, itemStartYUnscaled, logicalWidth, itemHeightUnscaled);
        }
        ctx.fillStyle = originalFillStyle;

        let noteIndicatorWidth = 0;
        if (item.hasNotes) {
            const indicatorSymbol = item.isNotesExpanded ? '▼' : '►';
            noteIndicatorWidth = ctx.measureText(indicatorSymbol).width + (fontSize * 0.5);
            ctx.fillText(indicatorSymbol, itemContentX, currentYUnscaled);
            itemContentX += noteIndicatorWidth;
        }
        let numberWidth = 0;
        if (item.itemNumber) {
            numberWidth = ctx.measureText(item.itemNumber).width + (fontSize * 0.5);
            ctx.fillText(item.itemNumber, itemContentX, currentYUnscaled);
        }
        itemContentX += numberWidth;

        const availableWidthForTitle = availableWidthForLineContent - (noteIndicatorWidth + numberWidth);
        let titleLineCountForAdvance = 0;
        if (item.title.text && availableWidthForTitle > fontSize * 0.5) {
            if (item.title.hasExplicitNewlines) {
                const titleLines = this.wrapText(ctx, item.title.text, availableWidthForTitle);
                titleLineCountForAdvance = titleLines.length > 0 ? titleLines.length : (item.title.text.trim() !== "" ? 1: 0);
                for (const line of titleLines) {
                    if (this.isDrawingOffCanvas(currentYUnscaled, yScaleFactor, logicalHeight, textLineHeight, totalMinimapRenderedHeightIfUnscaled, titleLines.length > 1)) break;
                    ctx.fillStyle = originalFillStyle;
                    ctx.fillText(line, itemContentX, currentYUnscaled);
                    currentYUnscaled += textLineHeight;
                }
                 if (titleLines.length === 0 && item.title.text.trim() !== "") currentYUnscaled += textLineHeight;
            } else {
                titleLineCountForAdvance = item.title.text.trim() !== "" ? 1: 0;
                if (!this.isDrawingOffCanvas(currentYUnscaled, yScaleFactor, logicalHeight, textLineHeight, totalMinimapRenderedHeightIfUnscaled) && item.title.text.trim() !== "") {
                   ctx.fillText(item.title.text, itemContentX, currentYUnscaled, availableWidthForTitle);
                }
                 currentYUnscaled += textLineHeight;
            }
        } else {
             titleLineCountForAdvance = item.title.text ? 1 : 0;
             currentYUnscaled += textLineHeight * titleLineCountForAdvance;
        }

        if (item.hasNotes && !item.isNotesExpanded && item.notePreview && item.notePreview.text) {
            ctx.fillStyle = `hsla(${fgH}, ${fgS}%, ${fgL + 20}%, 0.7)`;
            const notePreviewX = defaultXOffset + (effectiveNesting * indentSize) + (fontSize * 1.5);
            const availableWidthForNotePreview = logicalWidth - notePreviewX - (logicalWidth * 0.05);
            if (availableWidthForNotePreview > fontSize * 0.5) {
                if (item.notePreview.hasExplicitNewlines) {
                    const previewLines = this.wrapText(ctx, item.notePreview.text, availableWidthForNotePreview);
                    for (const line of previewLines) {
                         if (this.isDrawingOffCanvas(currentYUnscaled, yScaleFactor, logicalHeight, textLineHeight, totalMinimapRenderedHeightIfUnscaled, previewLines.length > 1)) break;
                        ctx.fillText(line, notePreviewX, currentYUnscaled);
                        currentYUnscaled += textLineHeight;
                    }
                    if (previewLines.length === 0 && item.notePreview.text.trim() !== "") currentYUnscaled += textLineHeight;
                } else {
                    if (!this.isDrawingOffCanvas(currentYUnscaled, yScaleFactor, logicalHeight, textLineHeight, totalMinimapRenderedHeightIfUnscaled) && item.notePreview.text.trim() !== "") {
                        ctx.fillText(item.notePreview.text, notePreviewX, currentYUnscaled, availableWidthForNotePreview);
                    }
                    currentYUnscaled += textLineHeight;
                }
            } else if (item.notePreview.text.trim() !== "") currentYUnscaled += textLineHeight;
            ctx.fillStyle = originalFillStyle;
        } else if (item.hasNotes && item.isNotesExpanded && item.expandedNotesText && item.expandedNotesText.length > 0) {
            ctx.fillStyle = `hsla(${fgH}, ${fgS}%, ${fgL + 15}%, 0.8)`;
            const noteSectionStartX = defaultXOffset + (effectiveNesting * indentSize) + (fontSize * 0.5);
            item.expandedNotesText.forEach((noteData, noteDataIdx) => {
                const noteTitleX = noteSectionStartX + (fontSize * 1);
                const availableWidthForNoteStrings = logicalWidth - noteTitleX - (logicalWidth * 0.05);
                if (noteData.title.text && availableWidthForNoteStrings > fontSize * 0.5) {
                    if (noteData.title.hasExplicitNewlines) {
                        const titleLines = this.wrapText(ctx, noteData.title.text, availableWidthForNoteStrings);
                        for (const line of titleLines) {
                            if (this.isDrawingOffCanvas(currentYUnscaled, yScaleFactor, logicalHeight, textLineHeight, totalMinimapRenderedHeightIfUnscaled, titleLines.length > 1)) break;
                            ctx.fillText(line, noteTitleX, currentYUnscaled);
                            currentYUnscaled += textLineHeight;
                        }
                        if (titleLines.length === 0 && noteData.title.text.trim() !== "") currentYUnscaled += textLineHeight;
                    } else {
                        if (!this.isDrawingOffCanvas(currentYUnscaled, yScaleFactor, logicalHeight, textLineHeight, totalMinimapRenderedHeightIfUnscaled) &&
                            typeof noteData.title.text === 'string' && noteData.title.text.trim() !== "") {
                           ctx.fillText(noteData.title.text, noteTitleX, currentYUnscaled, availableWidthForNoteStrings);
                        }
                        currentYUnscaled += textLineHeight;
                    }
                } else if (noteData.title.text) currentYUnscaled += textLineHeight;
                const noteContentX = noteTitleX + (fontSize * 1);
                const availableWidthForNoteContent = logicalWidth - noteContentX - (logicalWidth * 0.05);
                if (noteData.isExpanded) {
                    // Handle different content types with actual content rendering
                    if (noteData.contentType === IndividualNoteContentType.Image && noteData.contentDetails && typeof noteData.contentDetails === 'object' && 'type' in noteData.contentDetails) {
                        const imageDetails = noteData.contentDetails as { type: 'image'; src: string; width: number; height: number };

                        // Calculate scaled dimensions
                        let imageHeight = textLineHeight * 3; // Default height
                        let imageWidth = availableWidthForNoteContent * 0.6; // Default width

                        if (imageDetails.width && imageDetails.height) {
                            // Use actual image dimensions to maintain aspect ratio
                            const aspectRatio = imageDetails.width / imageDetails.height;
                            const maxHeight = textLineHeight * 8;
                            const maxWidth = availableWidthForNoteContent * 0.8;

                            // Scale to fit within bounds while maintaining aspect ratio
                            imageHeight = Math.min(maxHeight, noteData.actualHeightInMainPanel ?
                                Math.round(noteData.actualHeightInMainPanel / this.MAIN_PANEL_LINE_HEIGHT_EQUIVALENT_FOR_MINIMAP) * textLineHeight :
                                textLineHeight * 4);
                            imageWidth = Math.min(maxWidth, imageHeight * aspectRatio);

                            // Adjust height if width is constrained
                            if (imageWidth >= maxWidth) {
                                imageWidth = maxWidth;
                                imageHeight = imageWidth / aspectRatio;
                            }
                        }

                        if (!this.isDrawingOffCanvas(currentYUnscaled, yScaleFactor, logicalHeight, textLineHeight, totalMinimapRenderedHeightIfUnscaled, true)) {
                            const imageX = noteContentX;
                            const imageY = currentYUnscaled - textLineHeight * 0.8;

                            // Check if we have a cached image
                            const cachedImage = imageDetails.src ? this.state.loadedImages.get(imageDetails.src) : null;

                            if (cachedImage) {
                                // Draw the actual cached image
                                ctx.drawImage(cachedImage, imageX, imageY, imageWidth, imageHeight);
                            } else {
                                // Draw placeholder while image loads or if no src
                                ctx.fillStyle = `hsla(${fgH}, ${fgS}%, ${fgL + 20}%, 0.3)`;
                                ctx.fillRect(imageX, imageY, imageWidth, imageHeight);
                                ctx.strokeStyle = `hsla(${fgH}, ${fgS}%, ${fgL}%, 0.5)`;
                                ctx.lineWidth = 0.5;
                                ctx.strokeRect(imageX, imageY, imageWidth, imageHeight);
                                ctx.fillStyle = `hsla(${fgH}, ${fgS}%, ${fgL}%, 0.8)`;
                                ctx.fillText("IMG", imageX + 2, imageY + textLineHeight * 0.5);
                            }
                        }
                        currentYUnscaled += Math.ceil(imageHeight / textLineHeight) * textLineHeight;

                    } else if (noteData.contentType === IndividualNoteContentType.Video && noteData.contentDetails && typeof noteData.contentDetails === 'object' && 'type' in noteData.contentDetails) {
                        const videoDetails = noteData.contentDetails as { type: 'video'; src: string; width: number; height: number };

                        // Calculate scaled dimensions
                        let videoHeight = textLineHeight * 4; // Default height
                        let videoWidth = availableWidthForNoteContent * 0.7; // Default width

                        if (videoDetails.width && videoDetails.height) {
                            // Use actual video dimensions to maintain aspect ratio
                            const aspectRatio = videoDetails.width / videoDetails.height;
                            const maxHeight = textLineHeight * 6;
                            const maxWidth = availableWidthForNoteContent * 0.8;

                            // Scale to fit within bounds while maintaining aspect ratio
                            videoHeight = Math.min(maxHeight, noteData.actualHeightInMainPanel ?
                                Math.round(noteData.actualHeightInMainPanel / this.MAIN_PANEL_LINE_HEIGHT_EQUIVALENT_FOR_MINIMAP) * textLineHeight :
                                textLineHeight * 4);
                            videoWidth = Math.min(maxWidth, videoHeight * aspectRatio);

                            // Adjust height if width is constrained
                            if (videoWidth >= maxWidth) {
                                videoWidth = maxWidth;
                                videoHeight = videoWidth / aspectRatio;
                            }
                        }

                        if (!this.isDrawingOffCanvas(currentYUnscaled, yScaleFactor, logicalHeight, textLineHeight, totalMinimapRenderedHeightIfUnscaled, true)) {
                            const videoX = noteContentX;
                            const videoY = currentYUnscaled - textLineHeight * 0.8;
                            const centerX = videoX + videoWidth / 2;
                            const centerY = videoY + videoHeight / 2;

                            // Check if we have a cached video
                            const cachedVideo = videoDetails.src ? this.state.loadedVideos.get(videoDetails.src) : null;

                            if (cachedVideo) {
                                // Draw the actual cached video frame
                                ctx.drawImage(cachedVideo, videoX, videoY, videoWidth, videoHeight);

                                // Add play button overlay
                                ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
                                ctx.fillRect(videoX, videoY, videoWidth, videoHeight);

                                // Play button
                                ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                                ctx.beginPath();
                                ctx.moveTo(centerX - 4, centerY - 4);
                                ctx.lineTo(centerX + 4, centerY);
                                ctx.lineTo(centerX - 4, centerY + 4);
                                ctx.closePath();
                                ctx.fill();
                            } else {
                                // Draw placeholder while video loads or if no src
                                ctx.fillStyle = `hsla(${fgH}, ${fgS}%, ${fgL + 10}%, 0.4)`;
                                ctx.fillRect(videoX, videoY, videoWidth, videoHeight);
                                ctx.strokeStyle = `hsla(${fgH}, ${fgS}%, ${fgL}%, 0.6)`;
                                ctx.lineWidth = 0.5;
                                ctx.strokeRect(videoX, videoY, videoWidth, videoHeight);

                                // Play button
                                ctx.fillStyle = `hsla(${fgH}, ${fgS}%, ${fgL}%, 0.9)`;
                                ctx.beginPath();
                                ctx.moveTo(centerX - 3, centerY - 3);
                                ctx.lineTo(centerX + 3, centerY);
                                ctx.lineTo(centerX - 3, centerY + 3);
                                ctx.closePath();
                                ctx.fill();

                                ctx.fillText("VID", videoX + 2, videoY + textLineHeight * 0.5);
                            }
                        }
                        currentYUnscaled += Math.ceil(videoHeight / textLineHeight) * textLineHeight;

                    } else if (noteData.contentType === IndividualNoteContentType.File && noteData.contentDetails && typeof noteData.contentDetails === 'object' && 'type' in noteData.contentDetails) {
                        const fileDetails = noteData.contentDetails as { type: 'file'; src: string; fileName: string; fileType: string };

                        // Calculate file preview dimensions
                        let fileHeight = textLineHeight * 3; // Default height
                        let fileWidth = availableWidthForNoteContent * 0.8; // Default width

                        if (noteData.actualHeightInMainPanel && noteData.actualHeightInMainPanel > 0) {
                            const heightLines = Math.max(2, Math.min(6, Math.round(noteData.actualHeightInMainPanel / this.MAIN_PANEL_LINE_HEIGHT_EQUIVALENT_FOR_MINIMAP)));
                            fileHeight = heightLines * textLineHeight;
                        }

                        if (!this.isDrawingOffCanvas(currentYUnscaled, yScaleFactor, logicalHeight, textLineHeight, totalMinimapRenderedHeightIfUnscaled, true)) {
                            // Draw file preview
                            if (fileDetails.fileType === 'PDF' && fileDetails.src) {
                                // For PDFs, try to show iframe content or placeholder
                                ctx.fillStyle = `hsla(${fgH}, ${fgS}%, ${fgL + 5}%, 0.4)`;
                                ctx.fillRect(noteContentX, currentYUnscaled - textLineHeight * 0.8, fileWidth, fileHeight);
                                ctx.strokeStyle = `hsla(${fgH}, ${fgS}%, ${fgL}%, 0.6)`;
                                ctx.lineWidth = 0.5;
                                ctx.strokeRect(noteContentX, currentYUnscaled - textLineHeight * 0.8, fileWidth, fileHeight);

                                // Add PDF icon and text
                                ctx.fillStyle = `hsla(${fgH}, ${fgS}%, ${fgL}%, 0.9)`;
                                ctx.fillText("📄 PDF", noteContentX + 2, currentYUnscaled - textLineHeight * 0.3);

                                // Add filename if available
                                if (fileDetails.fileName && fileDetails.fileName !== 'Unknown File') {
                                    const fileName = fileDetails.fileName.length > 15 ?
                                        fileDetails.fileName.substring(0, 12) + '...' :
                                        fileDetails.fileName;
                                    ctx.fillStyle = `hsla(${fgH}, ${fgS}%, ${fgL - 10}%, 0.8)`;
                                    ctx.fillText(fileName, noteContentX + 2, currentYUnscaled + textLineHeight * 0.5);
                                }
                            } else {
                                // Generic file
                                ctx.fillStyle = `hsla(${fgH}, ${fgS}%, ${fgL + 15}%, 0.3)`;
                                ctx.fillRect(noteContentX, currentYUnscaled - textLineHeight * 0.8, fileWidth, fileHeight);
                                ctx.strokeStyle = `hsla(${fgH}, ${fgS}%, ${fgL}%, 0.5)`;
                                ctx.lineWidth = 0.5;
                                ctx.strokeRect(noteContentX, currentYUnscaled - textLineHeight * 0.8, fileWidth, fileHeight);

                                // Add file icon and type
                                ctx.fillStyle = `hsla(${fgH}, ${fgS}%, ${fgL}%, 0.9)`;
                                ctx.fillText(`📁 ${fileDetails.fileType}`, noteContentX + 2, currentYUnscaled - textLineHeight * 0.3);

                                // Add filename if available
                                if (fileDetails.fileName && fileDetails.fileName !== 'Unknown File') {
                                    const fileName = fileDetails.fileName.length > 15 ?
                                        fileDetails.fileName.substring(0, 12) + '...' :
                                        fileDetails.fileName;
                                    ctx.fillStyle = `hsla(${fgH}, ${fgS}%, ${fgL - 10}%, 0.8)`;
                                    ctx.fillText(fileName, noteContentX + 2, currentYUnscaled + textLineHeight * 0.5);
                                }
                            }
                        }
                        currentYUnscaled += Math.ceil(fileHeight / textLineHeight) * textLineHeight;

                    } else {
                        // Handle text and other content types
                        let placeholderText = "";
                        let placeholderLines = 1;

                        if (noteData.actualHeightInMainPanel && noteData.actualHeightInMainPanel > 0) {
                            placeholderLines = Math.max(1, Math.round(noteData.actualHeightInMainPanel / this.MAIN_PANEL_LINE_HEIGHT_EQUIVALENT_FOR_MINIMAP));
                        } else {
                            // Fallback to defaults if height wasn't captured
                            switch (noteData.contentType) {
                                case IndividualNoteContentType.Text: placeholderLines = 3; break;
                                case IndividualNoteContentType.File: placeholderLines = 2; break;
                                default: placeholderLines = 1; break;
                            }
                        }

                        // Determine placeholder text based on content type
                        switch (noteData.contentType) {
                            case IndividualNoteContentType.Text: placeholderText = "[Text Editor]"; break;
                            case IndividualNoteContentType.File: placeholderText = "[FILE]"; break;
                            default: placeholderText = "[Content]"; break;
                        }

                        if (!this.isDrawingOffCanvas(currentYUnscaled, yScaleFactor, logicalHeight, textLineHeight, totalMinimapRenderedHeightIfUnscaled, true)) {
                            this.drawPlaceholderBox(ctx, noteContentX, currentYUnscaled, availableWidthForNoteContent, placeholderLines, textLineHeight, placeholderText, `hsla(${fgH}, ${fgS}%, ${fgL + 30}%, 0.6)`);
                        }
                        currentYUnscaled += placeholderLines * textLineHeight;
                    }
                } else { // Collapsed individual note
                    if (noteData.contentDetails && typeof noteData.contentDetails === 'object') {
                        const snippet = noteData.contentDetails as TextSegment;
                        if (snippet.text && typeof snippet.text === 'string' && availableWidthForNoteContent > fontSize * 0.5) {
                            if (snippet.hasExplicitNewlines) {
                                const snippetLines = this.wrapText(ctx, snippet.text, availableWidthForNoteContent);
                                for (const line of snippetLines) {
                                    if (this.isDrawingOffCanvas(currentYUnscaled, yScaleFactor, logicalHeight, textLineHeight, totalMinimapRenderedHeightIfUnscaled, snippetLines.length > 1)) break;
                                    ctx.fillText(line, noteContentX, currentYUnscaled);
                                    currentYUnscaled += textLineHeight;
                                }
                                if (snippetLines.length === 0 && snippet.text.trim() !== "") currentYUnscaled += textLineHeight;
                            } else {
                                if (!this.isDrawingOffCanvas(currentYUnscaled, yScaleFactor, logicalHeight, textLineHeight, totalMinimapRenderedHeightIfUnscaled) &&
                                    snippet.text.trim() !== "") {
                                    ctx.fillText(snippet.text, noteContentX, currentYUnscaled, availableWidthForNoteContent);
                                }
                                currentYUnscaled += textLineHeight;
                            }
                        } else if (snippet.text && typeof snippet.text === 'string' && snippet.text.trim() !== "") currentYUnscaled += textLineHeight;
                        else if (snippet.text === "") currentYUnscaled += textLineHeight;
                    } else if (noteData.contentDetails === null || (typeof noteData.contentDetails === 'object' && !(noteData.contentDetails as TextSegment).text)) {
                        currentYUnscaled += textLineHeight;
                    }
                }
                if (noteDataIdx < item.expandedNotesText!.length - 1) {
                    if (!this.isDrawingOffCanvas(currentYUnscaled, yScaleFactor, logicalHeight, textLineHeight, totalMinimapRenderedHeightIfUnscaled)) {
                        const separatorY = currentYUnscaled + (textLineHeight / 2) - 1;
                        ctx.beginPath();
                        ctx.moveTo(noteSectionStartX, separatorY);
                        ctx.lineTo(logicalWidth - (logicalWidth * 0.05), separatorY);
                        ctx.strokeStyle = `hsla(${fgH}, ${fgS}%, ${fgL + 40}%, 0.5)`;
                        ctx.stroke();
                    }
                    currentYUnscaled += textLineHeight;
                }
            });
            ctx.fillStyle = originalFillStyle;
        }
    });
    ctx.restore();
    const { indicatorTop, indicatorHeight } = this.state;

    // totalMinimapRenderedHeightIfUnscaled is calculated at the beginning of drawMinimap
    // yScaleFactor is also defined (FIXED_ITEM_SCALE)
    const totalScaledMinimapContentHeight = totalMinimapRenderedHeightIfUnscaled * yScaleFactor;

    if (items.length === 0 || totalScaledMinimapContentHeight <= 0) {
        // No items or no height, don't draw indicator or ensure it's full/empty appropriately
        // If content is empty, calculateViewportIndicatorDimensions returns {0, 1}
        // So, if there's no content, indicator should reflect that (e.g. full height if contentRef is also empty)
        // For now, if no scaled height, just don't draw.
    } else if (indicatorHeight > 0 && indicatorHeight <= 1) {
        let indicatorActualTop = indicatorTop * totalScaledMinimapContentHeight;
        let indicatorActualHeight = indicatorHeight * totalScaledMinimapContentHeight;

        indicatorActualHeight = Math.max(2, indicatorActualHeight); // Min height of 2px for visibility
        indicatorActualTop = Math.max(0, indicatorActualTop);       // Cannot be above 0

        // Clamp indicator to be within the bounds of the totalScaledMinimapContentHeight
        if (indicatorActualTop + indicatorActualHeight > totalScaledMinimapContentHeight) {
            indicatorActualHeight = Math.max(2, totalScaledMinimapContentHeight - indicatorActualTop);
        }
        // Re-clamp top if height adjustment pushed it (e.g. if totalScaledMinimapContentHeight is very small)
        if (indicatorActualTop + indicatorActualHeight > totalScaledMinimapContentHeight) {
            indicatorActualTop = Math.max(0, totalScaledMinimapContentHeight - indicatorActualHeight);
        }

        const indicatorWidth = logicalWidth;

        // Only draw if the indicator has positive height and is at least partially within the scaled content height
        if (indicatorActualHeight > 0 && indicatorActualTop < totalScaledMinimapContentHeight) {
            ctx.fillStyle = 'rgba(120, 120, 120, 0.4)'; // Style as needed
            ctx.fillRect(0, indicatorActualTop, indicatorWidth, indicatorActualHeight);
        }
    }
}

  componentDidUpdate(prevProps: MinimapProps, prevState: MinimapState) {
    // Check if contentRef became available
    if (!prevProps.contentRef.current && this.props.contentRef.current) {
      this.initializeMinimap();
    }

    if (prevProps.contentRef !== this.props.contentRef) {
        if (prevProps.contentRef.current) prevProps.contentRef.current.removeEventListener('scroll', this.handleContentScroll);
        if (this.observer) this.observer.disconnect();
        if (this.props.contentRef.current && this.refMinimap.current && this.refCanvas.current) {
            this.canvasCtx = this.refCanvas.current.getContext('2d');
            this.props.contentRef.current.addEventListener('scroll', this.handleContentScroll);
            this.setCanvasDimensions();
            this.extractAndDrawMinimap();
            this.setupMutationObserver();
            this.updateScrollPercent();
        }
    }
    if (prevState.outlineItemsData !== this.state.outlineItemsData) this.drawMinimap();
    if (prevState.scrollPercent !== this.state.scrollPercent) { /* Already handled by updateScrollPercent */ }
    if (prevProps.highlightedItemId !== this.props.highlightedItemId || prevProps.activeParentItemIds !== this.props.activeParentItemIds) this.drawMinimap();
  }

  setupMutationObserver = () => {
    if (!this.props.contentRef.current) return;
    this.observer = new MutationObserver(() => {
        if (this.scrollTimeout) clearTimeout(this.scrollTimeout);
        this.scrollTimeout = setTimeout(() => { this.extractAndDrawMinimap(); }, 300);
    });
    this.observer.observe(this.props.contentRef.current, { childList: true, subtree: true, attributes: true, characterData: true });
  };

  handleContentScroll = () => { this.updateScrollPercent(); };
  handleResize = () => { this.setCanvasDimensions(); this.extractAndDrawMinimap(); this.updateScrollPercent(); };

  updateScrollPercent = () => {
    const contentElement = this.props.contentRef.current;
    if (!contentElement) return;
    const winScroll = contentElement.scrollTop;
    const height = contentElement.scrollHeight - contentElement.clientHeight;
    const scrolled = height > 0 ? winScroll / height : 0;
    this.setState({ scrollPercent: scrolled }, () => {
      const { newIndicatorTop, newIndicatorHeight } = this.calculateViewportIndicatorDimensions();
      this.setState({ indicatorTop: newIndicatorTop, indicatorHeight: newIndicatorHeight }, () => { this.drawMinimap(); });
    });
  };

  computeMinimapStyle = (): React.CSSProperties => ({ width: this.props.width, height: this.props.height, position: 'relative', overflow: 'hidden' });

  private calculateUnscaledMinimapItemHeight(
    item: OutlineItemData, ctx: CanvasRenderingContext2D, logicalWidth: number, fontSize: number, textLineHeight: number,
    defaultXOffset: number, indentSize: number, maxNestingForIndentCalc: number
  ): number {
    let itemHeightUnscaled = 0;
    ctx.font = `${fontSize}px ${this.MINIMAP_FONT_FAMILY}`;
    const effectiveNestingMeasure = Math.min(item.nestingLevel, maxNestingForIndentCalc);
    const itemContentXForTitleMeasure = defaultXOffset + (effectiveNestingMeasure * indentSize);
    let availableWidthForTitleMeasure = logicalWidth - itemContentXForTitleMeasure - (logicalWidth * 0.05);
    let noteIndicatorWidthMeasure = 0;
    if (item.hasNotes) noteIndicatorWidthMeasure = ctx.measureText(item.isNotesExpanded ? '▼' : '►').width + (fontSize * 0.5);
    let numberWidthMeasure = 0;
    if (item.itemNumber) numberWidthMeasure = ctx.measureText(item.itemNumber).width + (fontSize * 0.5);
    availableWidthForTitleMeasure -= (noteIndicatorWidthMeasure + numberWidthMeasure);
    let titleLineCount = 0;
    if (item.title.text && availableWidthForTitleMeasure > fontSize * 0.5) {
        if (item.title.hasExplicitNewlines) {
            const titleLines = this.wrapText(ctx, item.title.text, availableWidthForTitleMeasure);
            titleLineCount = titleLines.length > 0 ? titleLines.length : (item.title.text.trim() !== "" ? 1 : 0);
        } else titleLineCount = item.title.text.trim() !== "" ? 1 : 0;
    } else if (item.title.text) titleLineCount = 1;
    itemHeightUnscaled += titleLineCount * textLineHeight;

    if (item.hasNotes && !item.isNotesExpanded && item.notePreview && item.notePreview.text) {
        const notePreviewXMeasure = defaultXOffset + (effectiveNestingMeasure * indentSize) + (fontSize * 1.5);
        const availableWidthForNotePreviewMeasure = logicalWidth - notePreviewXMeasure - (logicalWidth * 0.05);
        let previewLineCount = 0;
        if (availableWidthForNotePreviewMeasure > fontSize * 0.5) {
            if (item.notePreview.hasExplicitNewlines) {
                const previewLines = this.wrapText(ctx, item.notePreview.text, availableWidthForNotePreviewMeasure);
                previewLineCount = previewLines.length > 0 ? previewLines.length : (item.notePreview.text.trim() !== "" ? 1 : 0);
            } else previewLineCount = item.notePreview.text.trim() !== "" ? 1 : 0;
        } else if (item.notePreview.text.trim() !== "") previewLineCount = 1;
        itemHeightUnscaled += previewLineCount * textLineHeight;
    } else if (item.hasNotes && item.isNotesExpanded && item.expandedNotesText && item.expandedNotesText.length > 0) {
        const noteContentIndentX = defaultXOffset + ((effectiveNestingMeasure + 1) * indentSize);
        const availableWidthForNoteContent = logicalWidth - noteContentIndentX - (logicalWidth * 0.05);
        let totalExpandedNoteSectionLineCount = 0;
        item.expandedNotesText.forEach((noteData, noteDataIdx) => {
            if (noteData.title.text && availableWidthForNoteContent > fontSize * 0.5) {
                if (noteData.title.hasExplicitNewlines) {
                    const titleLines = this.wrapText(ctx, noteData.title.text, availableWidthForNoteContent);
                    totalExpandedNoteSectionLineCount += titleLines.length > 0 ? titleLines.length : (noteData.title.text.trim() !== "" ? 1 : 0);
                } else totalExpandedNoteSectionLineCount += noteData.title.text.trim() !== "" ? 1 : 0;
            } else if (noteData.title.text) totalExpandedNoteSectionLineCount += 1;
            if (noteData.isExpanded) {
                if (noteData.actualHeightInMainPanel && noteData.actualHeightInMainPanel > 0) {
                    const calculatedMinimapLines = Math.max(1, Math.round(noteData.actualHeightInMainPanel / this.MAIN_PANEL_LINE_HEIGHT_EQUIVALENT_FOR_MINIMAP));
                    totalExpandedNoteSectionLineCount += calculatedMinimapLines;
                } else {
                    // Fallback to a default if height wasn't captured or is zero
                    switch (noteData.contentType) {
                        case IndividualNoteContentType.Text: totalExpandedNoteSectionLineCount += 3; break;
                        case IndividualNoteContentType.Image: case IndividualNoteContentType.Video: case IndividualNoteContentType.File: totalExpandedNoteSectionLineCount += 2; break;
                        default: totalExpandedNoteSectionLineCount += 1; break;
                    }
                }
            } else { // Collapsed individual note
                if (noteData.contentDetails && typeof noteData.contentDetails === 'object' && (noteData.contentDetails as TextSegment).text) {
                    const snippet = noteData.contentDetails as TextSegment;
                    if (availableWidthForNoteContent > fontSize * 0.5) {
                        if (snippet.hasExplicitNewlines) {
                            const snippetLines = this.wrapText(ctx, snippet.text, availableWidthForNoteContent);
                            totalExpandedNoteSectionLineCount += snippetLines.length > 0 ? snippetLines.length : (snippet.text.trim() !== "" ? 1 : 0);
                        } else totalExpandedNoteSectionLineCount += snippet.text.trim() !== "" ? 1 : 0;
                    } else if (snippet.text.trim() !== "") totalExpandedNoteSectionLineCount += 1;
                }
            }
            if (noteDataIdx < item.expandedNotesText!.length - 1) totalExpandedNoteSectionLineCount += 1;
        });
        itemHeightUnscaled += totalExpandedNoteSectionLineCount * textLineHeight;
    }
    if (itemHeightUnscaled === 0 && (item.title.text || (item.hasNotes && (item.notePreview || item.expandedNotesText)))) return textLineHeight;
    return Math.max(0, itemHeightUnscaled);
  }

  calculateViewportIndicatorDimensions = (): { newIndicatorTop: number, newIndicatorHeight: number } => {
    const contentEl = this.props.contentRef.current;
    const minimapEl = this.refMinimap.current;
    const canvas = this.refCanvas.current;
    if (!contentEl || !minimapEl || !canvas || minimapEl.clientHeight === 0 || this.state.outlineItemsData.length === 0) return { newIndicatorTop: 0, newIndicatorHeight: 1 };
    const ctx = this.canvasCtx;
    if (!ctx) return { newIndicatorTop: 0, newIndicatorHeight: 1 };
    const mainContentViewportTop = contentEl.scrollTop;
    const mainContentViewportHeight = contentEl.clientHeight;
    const mainContentViewportBottom = mainContentViewportTop + mainContentViewportHeight;
    let firstVisibleItemMinimapIdx = -1;
    let lastVisibleItemMinimapIdx = -1;
    this.state.outlineItemsData.forEach((item, index) => {
      const itemMainPanelTop = item.mainPanelOffsetTop;
      const itemMainPanelBottom = item.mainPanelOffsetTop + item.mainPanelOffsetHeight;
      const isVisible = itemMainPanelTop < mainContentViewportBottom && itemMainPanelBottom > mainContentViewportTop;
      if (isVisible) {
        if (firstVisibleItemMinimapIdx === -1) firstVisibleItemMinimapIdx = index;
        lastVisibleItemMinimapIdx = index;
      }
    });
    if (firstVisibleItemMinimapIdx === -1) {
      if (contentEl.scrollHeight <= contentEl.clientHeight) return { newIndicatorTop: 0, newIndicatorHeight: 1 };
      if (this.state.outlineItemsData.length > 0) {
        const firstItem = this.state.outlineItemsData[0];
        const lastItem = this.state.outlineItemsData[this.state.outlineItemsData.length - 1];
        if (mainContentViewportBottom <= firstItem.mainPanelOffsetTop) return { newIndicatorTop: 0, newIndicatorHeight: 0.02 };
        else if (mainContentViewportTop >= (lastItem.mainPanelOffsetTop + lastItem.mainPanelOffsetHeight)) return { newIndicatorTop: 0.98, newIndicatorHeight: 0.02 };
      }
      return { newIndicatorTop: 0, newIndicatorHeight: 0.02 };
    }
    const fontSize = 6;
    const textLineHeight = fontSize + 2;
    const logicalWidth = parseFloat(canvas.style.width || '0');
    const defaultXOffset = logicalWidth * 0.05;
    const indentSize = logicalWidth * 0.04;
    const maxNestingForIndentCalc = 5;
    const minimapItemUnscaledHeights = this.state.outlineItemsData.map(itemData =>
      this.calculateUnscaledMinimapItemHeight(
        itemData, ctx, logicalWidth, fontSize, textLineHeight, defaultXOffset, indentSize, maxNestingForIndentCalc
      )
    );
    let unscaledYTopForFirstVisible = 0;
    for (let i = 0; i < firstVisibleItemMinimapIdx; i++) unscaledYTopForFirstVisible += minimapItemUnscaledHeights[i];
    let unscaledHeightForVisibleItems = 0;
    for (let i = firstVisibleItemMinimapIdx; i <= lastVisibleItemMinimapIdx; i++) unscaledHeightForVisibleItems += minimapItemUnscaledHeights[i];
    const totalMinimapRenderedHeightIfUnscaled = minimapItemUnscaledHeights.reduce((sum, height) => sum + height, 0);
    if (totalMinimapRenderedHeightIfUnscaled === 0) return { newIndicatorTop: 0, newIndicatorHeight: 1 };
    const firstItem = this.state.outlineItemsData[firstVisibleItemMinimapIdx];
    const lastItem = this.state.outlineItemsData[lastVisibleItemMinimapIdx];
    const firstItemActualTop = firstItem.mainPanelOffsetTop;
    const firstItemActualHeight = firstItem.mainPanelOffsetHeight;
    const lastItemActualTop = lastItem.mainPanelOffsetTop;
    const lastItemActualHeight = lastItem.mainPanelOffsetHeight;
    let topCutoffRatio = 0;
    if (mainContentViewportTop > firstItemActualTop && firstItemActualHeight > 0) topCutoffRatio = (mainContentViewportTop - firstItemActualTop) / firstItemActualHeight;
    let bottomCutoffRatio = 0;
    if (mainContentViewportBottom < (lastItemActualTop + lastItemActualHeight) && lastItemActualHeight > 0) bottomCutoffRatio = ((lastItemActualTop + lastItemActualHeight) - mainContentViewportBottom) / lastItemActualHeight;
    const unscaledHeightOfFirstMinimapItem = minimapItemUnscaledHeights[firstVisibleItemMinimapIdx];
    const unscaledHeightOfLastMinimapItem = minimapItemUnscaledHeights[lastVisibleItemMinimapIdx];
    unscaledYTopForFirstVisible += topCutoffRatio * unscaledHeightOfFirstMinimapItem;
    let adjustedUnscaledHeightForVisible = unscaledHeightForVisibleItems - (topCutoffRatio * unscaledHeightOfFirstMinimapItem) - (bottomCutoffRatio * unscaledHeightOfLastMinimapItem);
    adjustedUnscaledHeightForVisible = Math.max(0, adjustedUnscaledHeightForVisible);
    const newIndicatorTopRatio = unscaledYTopForFirstVisible / totalMinimapRenderedHeightIfUnscaled;
    const newIndicatorHeightRatio = adjustedUnscaledHeightForVisible / totalMinimapRenderedHeightIfUnscaled;
    const minIndicatorHeightRatio = 0.02;
    return {
      newIndicatorTop: Math.max(0, Math.min(1 - minIndicatorHeightRatio, newIndicatorTopRatio)),
      newIndicatorHeight: Math.max(minIndicatorHeightRatio, Math.min(1, newIndicatorHeightRatio))
    };
  };

  render() {
    const minimapStyle = this.computeMinimapStyle();
    return (
      <div
        className={styles.minimapWindow}
        style={{...minimapStyle, cursor: this.state.isDraggingIndicator ? 'grabbing' : 'default' }}
        ref={this.refMinimap}
        onMouseDown={this.handleMouseDownOnMinimap}
      >
        <canvas
            ref={this.refCanvas}
            className={styles.minimapCanvas}
            style={{ position: 'absolute', top: 0, left: 0 }}
        />
      </div>
    );
  }
}
export default NewMinimap;
