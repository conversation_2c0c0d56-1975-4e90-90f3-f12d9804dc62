import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Shield, AlertTriangle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import { apiRequest } from "@/lib/queryClient";

export default function EmergencyAdminPage() {
  const [credentials, setCredentials] = useState({
    username: "",
    password: "",
    emergency_key: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const [, navigate] = useLocation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const res = await apiRequest("POST", "/api/admin/emergency-login", credentials);
      const user = await res.json();
      
      toast({
        title: "Emergency login successful",
        description: `Welcome back, ${user.username}. You can now access admin settings.`,
      });
      
      // Redirect to admin dashboard
      navigate("/admin");
    } catch (error: any) {
      toast({
        title: "Emergency login failed",
        description: error.message || "Invalid credentials or emergency key",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-100 flex items-center justify-center">
            <Shield className="h-8 w-8 text-red-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-red-600">
            Emergency Admin Access
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              This is an emergency bypass for when login is disabled. Only use this if you've been locked out of the admin panel.
            </AlertDescription>
          </Alert>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="username">Admin Username</Label>
              <Input
                id="username"
                value={credentials.username}
                onChange={(e) => setCredentials({ ...credentials, username: e.target.value })}
                placeholder="Enter admin username"
                required
              />
            </div>

            <div>
              <Label htmlFor="password">Admin Password</Label>
              <Input
                id="password"
                type="password"
                value={credentials.password}
                onChange={(e) => setCredentials({ ...credentials, password: e.target.value })}
                placeholder="Enter admin password"
                required
              />
            </div>

            <div>
              <Label htmlFor="emergency_key">Emergency Key</Label>
              <Input
                id="emergency_key"
                type="password"
                value={credentials.emergency_key}
                onChange={(e) => setCredentials({ ...credentials, emergency_key: e.target.value })}
                placeholder="Enter emergency key from environment"
                required
              />
              <p className="text-xs text-muted-foreground mt-1">
                This key is set in the EMERGENCY_ADMIN_KEY environment variable
              </p>
            </div>

            <Button 
              type="submit" 
              className="w-full bg-red-600 hover:bg-red-700" 
              disabled={isLoading}
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Emergency Login
            </Button>
          </form>

          <div className="text-center">
            <Button 
              variant="outline" 
              onClick={() => navigate("/")}
              className="text-sm"
            >
              Back to Home
            </Button>
          </div>

          <div className="text-xs text-muted-foreground space-y-2">
            <p><strong>How to set emergency key:</strong></p>
            <p>Set environment variable: <code className="bg-gray-100 px-1 rounded">EMERGENCY_ADMIN_KEY=your-secret-key</code></p>
            <p>Then restart the server and use that key above.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
