import fs from 'fs';
import 'dotenv/config';
import { defineConfig } from 'drizzle-kit';

const isProduction = process.env.NODE_ENV === "production";

export default defineConfig({
  schema: './shared/schema.ts',
  out: './migrations',
  dialect: 'postgresql',
  dbCredentials: {
    host: process.env.DB_HOST!,
    port: parseInt(process.env.DB_PORT!),
    user: process.env.DB_USER!,
    password: process.env.DB_PASSWORD!,
    database: process.env.DB_NAME!,
    ssl: isProduction ? {
      ca: fs.readFileSync('./certs/ca-certificate.crt').toString(),
      rejectUnauthorized: true,
    } : false,
  },
});