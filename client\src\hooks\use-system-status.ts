import { useQuery } from "@tanstack/react-query";

export interface SystemStatus {
  registrationEnabled: boolean;
  loginEnabled: boolean;
}

export function useSystemStatus() {
  return useQuery<SystemStatus>({
    queryKey: ['/api/system/status'],
    queryFn: async () => {
      const res = await fetch('/api/system/status');
      if (!res.ok) {
        throw new Error('Failed to fetch system status');
      }
      return res.json();
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchInterval: 1000 * 60 * 5, // Refetch every 5 minutes
  });
}
