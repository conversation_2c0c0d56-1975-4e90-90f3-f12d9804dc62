import React, { useRef, useState, useEffect } from 'react';
import { Editor } from '@/components/ui/editor';
import { AiEnhancedEditor } from '@/components/ui/ai-enhanced-editor';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Note, OutlineItem } from '@/lib/types';



interface NoteEditorProps {
  note: Note;
  outlineItems: OutlineItem[];
  onUpdate: (note: Note, forceSaveNow?: boolean) => void;
  onDelete: () => void;
  onFileUpload: (file: File) => Promise<string>;
}

export function NoteEditor({ note, outlineItems, onUpdate, onDelete, onFileUpload }: NoteEditorProps) {
  const [isDraggingFile, setIsDraggingFile] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onUpdate({
      ...note,
      title: e.target.value,
      updatedAt: new Date().toISOString()
    });
  };
  
  const handleContentChange = (content: string) => {
    onUpdate({
      ...note,
      content,
      updatedAt: new Date().toISOString()
    });
  };
  
  const handleOutlineLinkChange = (outlineId: string) => {
    let linkedOutlineIds = note.linkedOutlineIds || [];
    
    if (outlineId === "unlink") {
      linkedOutlineIds = [];
    } else {
      // If it's already linked, we don't need to add it again
      if (!linkedOutlineIds.includes(outlineId)) {
        linkedOutlineIds = [...linkedOutlineIds, outlineId];
      }
    }
    
    onUpdate({
      ...note,
      linkedOutlineIds,
      updatedAt: new Date().toISOString()
    });
  };
  
  const handleFileDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDraggingFile(true);
  };
  
  const handleFileDragLeave = () => {
    setIsDraggingFile(false);
  };
  
  const handleFileDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDraggingFile(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      
      try {
        const fileUrl = await onFileUpload(file);
        
        // Handle different file types
        if (file.type.startsWith('image/')) {
          // Add the image to the note's content
          const imageHtml = `
<div class="my-4 border rounded-lg p-2 bg-neutral-50">
  <div class="flex items-center justify-between mb-2">
    <span class="text-sm font-medium">${file.name}</span>
  </div>
  <div class="bg-white rounded border border-neutral-200 flex items-center justify-center">
    <img src="${fileUrl}" alt="${file.name}" class="max-h-40">
  </div>
</div>`;
          
          const updatedNote = {
            ...note,
            content: note.content + imageHtml,
            imageUrls: [...(note.imageUrls || []), fileUrl],
            updatedAt: new Date().toISOString()
          };
          
          // Update with forceSave flag set to true to save immediately
          onUpdate(updatedNote, true);
        } 
        else if (file.type.startsWith('video/')) {
          // Add the video to the note's content
          const videoHtml = `
<div class="my-4 border rounded-lg p-2 bg-neutral-50">
  <div class="flex items-center justify-between mb-2">
    <span class="text-sm font-medium">${file.name}</span>
  </div>
  <div class="bg-white rounded border border-neutral-200 flex items-center justify-center">
    <video controls class="max-h-40 max-w-full">
      <source src="${fileUrl}" type="${file.type}">
      Your browser does not support the video tag.
    </video>
  </div>
</div>`;
          
          const updatedNote = {
            ...note,
            content: note.content + videoHtml,
            videoUrls: [...(note.videoUrls || []), fileUrl],
            updatedAt: new Date().toISOString()
          };
          
          // Update with forceSave flag set to true to save immediately
          onUpdate(updatedNote, true);
        }
        else {
          // Generic file attachment
          const fileHtml = `
<div class="my-4 border rounded-lg p-2 bg-neutral-50">
  <div class="flex items-center justify-between mb-2">
    <span class="text-sm font-medium">${file.name}</span>
  </div>
  <div class="p-3 bg-white rounded border border-neutral-200 flex items-center justify-center">
    <a href="${fileUrl}" target="_blank" class="flex items-center text-primary-600">
      <i class="ri-file-line mr-2 text-xl"></i>
      ${file.name}
    </a>
  </div>
</div>`;
          
          const updatedNote = {
            ...note,
            content: note.content + fileHtml,
            fileUrls: [...(note.fileUrls || []), fileUrl],
            updatedAt: new Date().toISOString()
          };
          
          // Update with forceSave flag set to true to save immediately
          onUpdate(updatedNote, true);
        }
      } catch (error) {
        console.error('Failed to upload file:', error);
      }
    }
  };
  
  const handleFileButtonClick = () => {
    fileInputRef.current?.click();
  };
  
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      
      try {
        const fileUrl = await onFileUpload(file);
        
        // Handle different file types
        if (file.type.startsWith('image/')) {
          // Add the image to the note's content
          const imageHtml = `
<div class="my-4 border rounded-lg p-2 bg-neutral-50">
  <div class="flex items-center justify-between mb-2">
    <span class="text-sm font-medium">${file.name}</span>
  </div>
  <div class="bg-white rounded border border-neutral-200 flex items-center justify-center">
    <img src="${fileUrl}" alt="${file.name}" class="max-h-40">
  </div>
</div>`;
          
          const updatedNote = {
            ...note,
            content: note.content + imageHtml,
            imageUrls: [...(note.imageUrls || []), fileUrl],
            updatedAt: new Date().toISOString()
          };
          
          // Update with forceSave flag set to true to save immediately
          onUpdate(updatedNote, true);
        } 
        else if (file.type.startsWith('video/')) {
          // Add the video to the note's content
          const videoHtml = `
<div class="my-4 border rounded-lg p-2 bg-neutral-50">
  <div class="flex items-center justify-between mb-2">
    <span class="text-sm font-medium">${file.name}</span>
  </div>
  <div class="bg-white rounded border border-neutral-200 flex items-center justify-center">
    <video controls class="max-h-40 max-w-full">
      <source src="${fileUrl}" type="${file.type}">
      Your browser does not support the video tag.
    </video>
  </div>
</div>`;
          
          const updatedNote = {
            ...note,
            content: note.content + videoHtml,
            videoUrls: [...(note.videoUrls || []), fileUrl],
            updatedAt: new Date().toISOString()
          };
          
          // Update with forceSave flag set to true to save immediately
          onUpdate(updatedNote, true);
        }
        else {
          // Generic file attachment
          const fileHtml = `
<div class="my-4 border rounded-lg p-2 bg-neutral-50">
  <div class="flex items-center justify-between mb-2">
    <span class="text-sm font-medium">${file.name}</span>
  </div>
  <div class="p-3 bg-white rounded border border-neutral-200 flex items-center justify-center">
    <a href="${fileUrl}" target="_blank" class="flex items-center text-primary-600">
      <i class="ri-file-line mr-2 text-xl"></i>
      ${file.name}
    </a>
  </div>
</div>`;
          
          const updatedNote = {
            ...note,
            content: note.content + fileHtml,
            fileUrls: [...(note.fileUrls || []), fileUrl],
            updatedAt: new Date().toISOString()
          };
          
          // Update with forceSave flag set to true to save immediately
          onUpdate(updatedNote, true);
        }
      } catch (error) {
        console.error('Failed to upload file:', error);
      }
    }
    
    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  const getWordCount = (text: string): number => {
    return text.trim().split(/\s+/).filter(Boolean).length;
  };

  return (
    <>
      <div className="p-4 border-b border-neutral-200 flex justify-between items-center">
        <div className="flex-1 relative">
          <div className="flex items-center gap-2">
            <Input
              placeholder="Note title"
              value={note.title || ''}
              onChange={handleTitleChange}
              className="font-medium bg-transparent border-none p-0 focus:ring-0 focus:outline-none"
            />

          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <span className="text-sm text-neutral-600">Link to:</span>
            <Select 
              value={note.linkedOutlineIds && note.linkedOutlineIds.length > 0 ? note.linkedOutlineIds[0] : "unlink"}
              onValueChange={handleOutlineLinkChange}
            >
              <SelectTrigger className="w-auto min-w-[240px] h-8 text-sm py-1">
                <SelectValue placeholder="Select outline item" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="unlink">Unlink</SelectItem>
                {outlineItems.map(item => (
                  <SelectItem key={item.id} value={item.id}>{item.number} {item.title}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Button 
            variant="ghost" 
            size="icon"
            className="text-neutral-500 hover:text-danger-500"
            onClick={onDelete}
          >
            <i className="ri-delete-bin-line"></i>
          </Button>
        </div>
      </div>

      <div className="flex-1 p-4 overflow-y-auto relative">
        <div className="prose max-w-none">
          <AiEnhancedEditor 
            value={note.content} 
            onChange={handleContentChange}
            minHeight="200px"
            className="w-full h-full"
            onShowSubscriptionModal={() => {}}
          />
        </div>

        {/* Drag and drop zone for file attachments */}
        <div 
          className={`border-2 border-dashed ${
            isDraggingFile ? 'border-primary-400 bg-primary-50' : 'border-neutral-300'
          } rounded-lg p-4 mt-4 text-center transition-colors`}
          onDragOver={handleFileDragOver}
          onDragLeave={handleFileDragLeave}
          onDrop={handleFileDrop}
          onClick={handleFileButtonClick}
        >
          <i className="ri-attachment-line text-2xl text-neutral-400"></i>
          <p className="text-sm text-neutral-500 mt-1">
            {((note.imageUrls && note.imageUrls.length > 0) || 
              (note.videoUrls && note.videoUrls.length > 0) || 
              (note.fileUrls && note.fileUrls.length > 0))
              ? "Drop more files here or click to add"
              : "Drag and drop files here or click to upload"}
          </p>
          <input 
            type="file" 
            className="hidden" 
            accept="*/*" 
            ref={fileInputRef}
            onChange={handleFileChange}
          />
        </div>
        
        {/* Attachment list */}
        {((note.imageUrls && note.imageUrls.length > 0) || 
          (note.videoUrls && note.videoUrls.length > 0) || 
          (note.fileUrls && note.fileUrls.length > 0)) && (
          <div className="mt-4 border rounded-lg p-3 bg-neutral-50">
            <h3 className="text-sm font-medium mb-2">Attachments</h3>
            <ul className="space-y-1">
              {/* Images */}
              {note.imageUrls?.map((url, index) => {
                const fileName = url.split('/').pop() || 'Image';
                return (
                  <li key={`img-${index}`} className="text-sm flex items-center">
                    <i className="ri-image-line mr-1 text-neutral-500"></i>
                    <a 
                      href={url} 
                      target="_blank" 
                      className="text-primary-600 hover:underline"
                    >
                      {fileName}
                    </a>
                  </li>
                );
              })}
              
              {/* Videos */}
              {note.videoUrls?.map((url, index) => {
                const fileName = url.split('/').pop() || 'Video';
                return (
                  <li key={`vid-${index}`} className="text-sm flex items-center">
                    <i className="ri-video-line mr-1 text-neutral-500"></i>
                    <a 
                      href={url} 
                      target="_blank" 
                      className="text-primary-600 hover:underline"
                    >
                      {fileName}
                    </a>
                  </li>
                );
              })}
              
              {/* Other files */}
              {note.fileUrls?.map((url, index) => {
                const fileName = url.split('/').pop() || 'File';
                return (
                  <li key={`file-${index}`} className="text-sm flex items-center">
                    <i className="ri-file-line mr-1 text-neutral-500"></i>
                    <a 
                      href={url} 
                      target="_blank" 
                      className="text-primary-600 hover:underline"
                    >
                      {fileName}
                    </a>
                  </li>
                );
              })}
            </ul>
          </div>
        )}
      </div>

      {/* Note editor toolbar */}
      <div className="p-2 border-t border-neutral-200 flex justify-between items-center">
        <div className="flex gap-2">
          <Button variant="ghost" size="icon" className="p-1.5 text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 rounded">
            <i className="ri-bold"></i>
          </Button>
          <Button variant="ghost" size="icon" className="p-1.5 text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 rounded">
            <i className="ri-italic"></i>
          </Button>
          <Button variant="ghost" size="icon" className="p-1.5 text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 rounded">
            <i className="ri-list-unordered"></i>
          </Button>
          <Button variant="ghost" size="icon" className="p-1.5 text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 rounded">
            <i className="ri-list-ordered"></i>
          </Button>
          <Button variant="ghost" size="icon" className="p-1.5 text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 rounded">
            <i className="ri-double-quotes-l"></i>
          </Button>
          <Button variant="ghost" size="icon" className="p-1.5 text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 rounded">
            <i className="ri-link"></i>
          </Button>
          <Button 
            variant="ghost" 
            size="icon" 
            className="p-1.5 text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 rounded"
            onClick={handleFileButtonClick}
          >
            <i className="ri-attachment-line"></i>
          </Button>
        </div>
        <div className="text-sm text-neutral-500">
          {getWordCount(note.content)} words
        </div>
      </div>
    </>
  );
}