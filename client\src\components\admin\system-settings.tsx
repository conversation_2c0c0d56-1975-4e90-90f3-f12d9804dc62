import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Loader2, Settings, Save } from "lucide-react";
import { useAdminSettings, useUpdateSystemSetting } from "@/hooks/use-admin-api";
import { SystemSetting } from "@shared/schema";

export function SystemSettings() {
  const { data: settings, isLoading, error } = useAdminSettings();
  const updateSettingMutation = useUpdateSystemSetting();

  const [localSettings, setLocalSettings] = useState<Record<string, string>>({});
  const [hasChanges, setHasChanges] = useState(false);

  // Initialize local settings when data loads
  React.useEffect(() => {
    if (settings) {
      const settingsMap = settings.reduce((acc: Record<string, string>, setting: SystemSetting) => {
        acc[setting.key] = setting.value;
        return acc;
      }, {});
      setLocalSettings(settingsMap);
    }
  }, [settings]);

  const handleSettingChange = (key: string, value: string) => {
    setLocalSettings(prev => ({
      ...prev,
      [key]: value
    }));
    setHasChanges(true);
  };

  const handleSaveSettings = async () => {
    try {
      const promises = Object.entries(localSettings).map(([key, value]) => {
        const originalSetting = settings?.find((s: SystemSetting) => s.key === key);
        if (originalSetting && originalSetting.value !== value) {
          return updateSettingMutation.mutateAsync({
            key,
            value,
            description: originalSetting.description || undefined
          });
        }
        return Promise.resolve();
      });

      await Promise.all(promises);
      setHasChanges(false);
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const getBooleanValue = (key: string): boolean => {
    return localSettings[key] === "true";
  };

  const setBooleanValue = (key: string, value: boolean) => {
    handleSettingChange(key, value.toString());
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center text-red-600">
        Error loading settings: {error.message}
      </div>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          System Settings
        </CardTitle>
        {hasChanges && (
          <Button
            onClick={handleSaveSettings}
            disabled={updateSettingMutation.isPending}
          >
            {updateSettingMutation.isPending && (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            )}
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        )}
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="registration-enabled">Allow New Registration</Label>
              <p className="text-sm text-muted-foreground">
                When disabled, new users cannot create accounts
              </p>
            </div>
            <Switch
              id="registration-enabled"
              checked={getBooleanValue("registration_enabled")}
              onCheckedChange={(checked) => setBooleanValue("registration_enabled", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="login-enabled">Allow User Login</Label>
              <p className="text-sm text-muted-foreground">
                When disabled, users cannot log into their accounts
              </p>
              <p className="text-xs text-orange-600">
                ⚠️ If you disable this and logout, use /emergency-admin to regain access
              </p>
            </div>
            <Switch
              id="login-enabled"
              checked={getBooleanValue("login_enabled")}
              onCheckedChange={(checked) => setBooleanValue("login_enabled", checked)}
            />
          </div>
        </div>

        {settings && settings.length > 0 && (
          <div className="mt-8">
            <h3 className="text-lg font-medium mb-4">All Settings</h3>
            <div className="space-y-2">
              {settings.map((setting: SystemSetting) => (
                <div key={setting.key} className="flex justify-between items-center p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">{setting.key}</div>
                    {setting.description && (
                      <div className="text-sm text-muted-foreground">{setting.description}</div>
                    )}
                  </div>
                  <div className="font-mono text-sm">
                    {localSettings[setting.key] || setting.value}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
