import React, { useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Note, OutlineItem } from '@/lib/types';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface MarkdownNoteEditorProps {
  note: Note;
  outlineItems: OutlineItem[];
  onUpdate: (note: Note, forceSaveNow?: boolean) => void;
  onDelete: () => void;
  onFileUpload: (file: File) => Promise<string>;
}

export function MarkdownNoteEditor({ 
  note, 
  outlineItems, 
  onUpdate, 
  onDelete, 
  onFileUpload 
}: MarkdownNoteEditorProps) {
  const [isEditMode, setIsEditMode] = useState(true);
  const [isDraggingImage, setIsDraggingImage] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onUpdate({
      ...note,
      title: e.target.value,
      updatedAt: new Date().toISOString()
    });
  };
  
  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onUpdate({
      ...note,
      content: e.target.value,
      updatedAt: new Date().toISOString()
    });
  };
  
  const handleOutlineLinkChange = (outlineId: string) => {
    const linkedOutlineId = outlineId === "unlink" ? "" : outlineId;

    onUpdate({
      ...note,
      linkedOutlineId,
      updatedAt: new Date().toISOString()
    });
  };
  
  const handleImageDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDraggingImage(true);
  };
  
  const handleImageDragLeave = () => {
    setIsDraggingImage(false);
  };
  
  const handleImageDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDraggingImage(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      if (file.type.startsWith('image/')) {
        try {
          const imageUrl = await onFileUpload(file);
          
          // Add the image in Markdown format
          const imageMarkdown = `\n\n![${file.name}](${imageUrl})\n\n`;
          
          const updatedNote = {
            ...note,
            content: note.content + imageMarkdown,
            imageUrls: [...(note.imageUrls || []), imageUrl],
            updatedAt: new Date().toISOString()
          };
          
          // Update with forceSave flag set to true to save immediately
          onUpdate(updatedNote, true);
        } catch (error) {
          console.error('Failed to upload image:', error);
        }
      }
    }
  };
  
  const handleImageButtonClick = () => {
    fileInputRef.current?.click();
  };
  
  const handleImageFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      if (file.type.startsWith('image/')) {
        try {
          const imageUrl = await onFileUpload(file);
          
          // Add the image in Markdown format
          const imageMarkdown = `\n\n![${file.name}](${imageUrl})\n\n`;
          
          const updatedNote = {
            ...note,
            content: note.content + imageMarkdown,
            imageUrls: [...(note.imageUrls || []), imageUrl],
            updatedAt: new Date().toISOString()
          };
          
          // Update with forceSave flag set to true to save immediately
          onUpdate(updatedNote, true);
        } catch (error) {
          console.error('Failed to upload image:', error);
        }
      }
    }
    
    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  // Clean up Markdown formatting for preview and word counting
  const stripMarkdown = (markdown: string): string => {
    return markdown
      .replace(/#+\s+/g, '') // Remove headings
      .replace(/(\*\*|__)(.*?)\1/g, '$2') // Remove bold
      .replace(/(\*|_)(.*?)\1/g, '$2') // Remove italic
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Replace links with just the text
      .replace(/!\[([^\]]*)\]\([^)]+\)/g, '') // Remove images
      .replace(/```[\s\S]*?```/g, '') // Remove code blocks
      .replace(/`([^`]+)`/g, '$1') // Remove inline code
      .replace(/\n+/g, ' ') // Replace newlines with spaces
      .replace(/\s+/g, ' ') // Replace multiple spaces with a single space
      .replace(/>\s(.*)/g, '$1') // Remove blockquotes
      .replace(/^\s*[-*+]\s+/gm, '') // Remove unordered list markers
      .replace(/^\s*\d+\.\s+/gm, ''); // Remove ordered list markers
  };
  
  const getWordCount = (text: string): number => {
    return stripMarkdown(text).trim().split(/\s+/).filter(Boolean).length;
  };

  // Markdown toolbar handlers
  const insertMarkdown = (markdownTemplate: string, placeholder: string = 'text') => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);
    
    const textToInsert = selectedText.length > 0
      ? markdownTemplate.replace(placeholder, selectedText)
      : markdownTemplate;
      
    const newContent = 
      textarea.value.substring(0, start) + 
      textToInsert + 
      textarea.value.substring(end);
      
    onUpdate({
      ...note,
      content: newContent,
      updatedAt: new Date().toISOString()
    });
    
    // Focus back on textarea and set cursor position after inserted text
    setTimeout(() => {
      textarea.focus();
      const newCursorPos = start + textToInsert.length;
      textarea.setSelectionRange(newCursorPos, newCursorPos);
    }, 0);
  };

  const handleBoldClick = () => insertMarkdown('**text**', 'text');
  const handleItalicClick = () => insertMarkdown('*text*', 'text');
  const handleUnorderedListClick = () => insertMarkdown('\n- Item 1\n- Item 2\n- Item 3\n');
  const handleOrderedListClick = () => insertMarkdown('\n1. Item 1\n2. Item 2\n3. Item 3\n');
  const handleQuoteClick = () => insertMarkdown('\n> text\n', 'text');
  const handleLinkClick = () => insertMarkdown('[title](url)', 'title');
  const handleCodeClick = () => insertMarkdown('`code`', 'code');
  const handleHeadingClick = () => insertMarkdown('\n## Heading\n', 'Heading');

  return (
    <>
      <div className="p-4 border-b border-neutral-200 flex justify-between items-center">
        <Input
          type="text"
          placeholder="Note title"
          value={note.title}
          onChange={handleTitleChange}
          className="font-medium flex-1 bg-transparent border-none p-0 focus:ring-0 focus:outline-none"
        />
        
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <span className="text-sm text-neutral-600">Link to:</span>
            <Select
              value={note.linkedOutlineId || "unlink"}
              onValueChange={handleOutlineLinkChange}
            >
              <SelectTrigger className="w-auto min-w-[240px] h-8 text-sm py-1">
                <SelectValue placeholder="Select outline item" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="unlink">Unlink</SelectItem>
                {outlineItems.map((item, index) => (
                  <SelectItem key={`${item.id}-${index}`} value={item.id}>{item.number} {item.title}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Button 
            variant="ghost" 
            size="icon"
            className="text-neutral-500 hover:text-danger-500"
            onClick={onDelete}
          >
            <i className="ri-delete-bin-line"></i>
          </Button>
        </div>
      </div>

      <div className="flex-1 p-4 overflow-y-auto relative flex flex-col">
        {isEditMode ? (
          <div className="flex flex-col h-full">
            <textarea
              ref={textareaRef}
              value={note.content}
              onChange={handleContentChange}
              className="w-full flex-grow p-2 border border-neutral-200 rounded-md 
                focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500
                font-mono text-sm resize-none overflow-auto mb-3"
              placeholder="Write your note content here using Markdown..."
            />
            
            <div 
              className={`border-2 border-dashed h-16 flex-shrink-0 ${
                isDraggingImage ? 'border-primary-400 bg-primary-50' : 'border-neutral-300'
              } rounded-lg p-2 text-center transition-colors cursor-pointer flex items-center justify-center mb-3`}
              onDragOver={handleImageDragOver}
              onDragLeave={handleImageDragLeave}
              onDrop={handleImageDrop}
              onClick={handleImageButtonClick}
            >
              <div className="flex items-center">
                <i className="ri-image-add-line text-xl text-neutral-400 mr-2"></i>
                <p className="text-sm text-neutral-500">
                  {(note.imageUrls && note.imageUrls.length > 0)
                    ? "Drop more images here or click to add"
                    : "Drag and drop images here or click to upload"}
                </p>
              </div>
              <input 
                type="file" 
                className="hidden" 
                accept="image/*" 
                ref={fileInputRef}
                onChange={handleImageFileChange}
              />
            </div>
            
            {/* Attachment list */}
            {(note.imageUrls && note.imageUrls.length > 0) && (
              <div className="mb-3">
                <h3 className="text-sm font-medium mb-2">Attached Images ({note.imageUrls.length})</h3>
                <ul className="list-disc pl-5 space-y-1">
                  {note.imageUrls.map((url, index) => {
                    const fileName = url.split('/').pop() || 'Image';
                    return (
                      <li key={index} className="text-sm">
                        <a 
                          href={url} 
                          target="_blank" 
                          className="flex items-center hover:underline text-primary cursor-pointer"
                        >
                          <i className="ri-image-line mr-1"></i>
                          {fileName}
                        </a>
                      </li>
                    );
                  })}
                </ul>
              </div>
            )}
          </div>
        ) : (
          <div className="flex flex-col h-full">
            <div className="prose max-w-none border border-neutral-200 rounded-md p-4 flex-grow overflow-auto bg-white mb-3">
              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                {note.content}
              </ReactMarkdown>
            </div>
            <div className="h-16 flex-shrink-0 rounded-lg p-2 flex items-center justify-center bg-neutral-50 border border-neutral-200">
              <div className="flex items-center">
                <i className="ri-information-line text-xl text-neutral-400 mr-2"></i>
                <p className="text-sm text-neutral-500">
                  Switch to edit mode to upload or add images
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Note editor toolbar */}
      <div className="p-2 border-t border-neutral-200 flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            className={`text-xs ${isEditMode ? 'bg-primary-50 text-primary-700' : ''}`}
            onClick={() => setIsEditMode(true)}
          >
            <i className="ri-edit-line mr-1"></i> Edit
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            className={`text-xs ${!isEditMode ? 'bg-primary-50 text-primary-700' : ''}`}
            onClick={() => setIsEditMode(false)}
          >
            <i className="ri-eye-line mr-1"></i> Preview
          </Button>
          
          {isEditMode && (
            <div className="flex gap-1 ml-2 border-l border-neutral-200 pl-2">
              <Button variant="ghost" size="icon" className="p-1.5 text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 rounded" onClick={handleBoldClick}>
                <i className="ri-bold"></i>
              </Button>
              <Button variant="ghost" size="icon" className="p-1.5 text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 rounded" onClick={handleItalicClick}>
                <i className="ri-italic"></i>
              </Button>
              <Button variant="ghost" size="icon" className="p-1.5 text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 rounded" onClick={handleHeadingClick}>
                <i className="ri-heading"></i>
              </Button>
              <Button variant="ghost" size="icon" className="p-1.5 text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 rounded" onClick={handleUnorderedListClick}>
                <i className="ri-list-unordered"></i>
              </Button>
              <Button variant="ghost" size="icon" className="p-1.5 text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 rounded" onClick={handleOrderedListClick}>
                <i className="ri-list-ordered"></i>
              </Button>
              <Button variant="ghost" size="icon" className="p-1.5 text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 rounded" onClick={handleQuoteClick}>
                <i className="ri-double-quotes-l"></i>
              </Button>
              <Button variant="ghost" size="icon" className="p-1.5 text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 rounded" onClick={handleLinkClick}>
                <i className="ri-link"></i>
              </Button>
              <Button variant="ghost" size="icon" className="p-1.5 text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 rounded" onClick={handleCodeClick}>
                <i className="ri-code-line"></i>
              </Button>
              <Button 
                variant="ghost" 
                size="icon" 
                className="p-1.5 text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 rounded"
                onClick={handleImageButtonClick}
              >
                <i className="ri-image-add-line"></i>
              </Button>
            </div>
          )}
        </div>
        <div className="text-sm text-neutral-500">
          {getWordCount(note.content)} words
        </div>
      </div>
    </>
  );
}